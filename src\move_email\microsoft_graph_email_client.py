import json
import requests
import urllib.parse

from microsoft_oauth2_client import MicrosoftOAuth2Client

MAIL_FOLDERS_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/{user_id}/mailFolders?$top=20'
MAIL_SUBFOLDERS_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/{user_id}/mailFolders/Inbox/childFolders'
MOVE_EMAIL_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/{user_id}/messages/{email_id}/move'
FOLDER_MESSAGES_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/{user_id}/mailFolders/{folder_id}/messages'
GET_EMAIL_FROM_INTERNET_MESSAGE_ID = "https://graph.microsoft.com/v1.0/users/{user_id}/messages?$filter=internetMessageId eq \'{email_internet_message_id}\'"

class MicrosoftGraphEmailClient:
    """Class to manage folders and emails in Microsoft Graph"""

    def __init__(self, user_id):
        self.user_id = user_id
        self.oauth_client = MicrosoftOAuth2Client()
        self.access_token = self.get_access_token()

    def get_access_token(self):
        """Gets the access token from MicrosoftOAuth2Client"""
        return self.oauth_client.get_access_token()

    def get_folder_id(self, folder_name, endpoint=MAIL_FOLDERS_ENDPOINT):
        """Retrieves the folder ID based on its name"""
        endpoint = endpoint.format(user_id=self.user_id)

        if self.access_token:
            headers = {'Authorization': f'Bearer {self.access_token}'}
            response = requests.get(endpoint, headers=headers)

            if response.status_code == 200:
                folders = response.json().get('value', [])
                for folder in folders:
                    if folder['displayName'] == folder_name:
                        return folder['id']
            else:
                print('Error fetching folders:', response.json())
                
        
        if endpoint == MAIL_FOLDERS_ENDPOINT.format(user_id=self.user_id):
            return self.get_folder_id(folder_name, MAIL_SUBFOLDERS_ENDPOINT)
        
        return None
    
    def get_id_from_email(self, email_internet_message_id):
        """Retrieves the email ID based on its internet message ID"""
        email_id = urllib.parse.quote(email_internet_message_id)
        endpoint = GET_EMAIL_FROM_INTERNET_MESSAGE_ID.format(user_id=self.user_id, email_internet_message_id=email_id)
 
        if self.access_token:
            headers = {'Authorization': f'Bearer {self.access_token}'}
            response = requests.get(endpoint, headers=headers)
 
            if response.status_code == 200:
                emails = response.json().get('value', [])
                for email in emails:
                    return email['id']
            else:
                print('Error fetching emails:', response.json())
                
        return None
        
    def move_email(self, email_id, destination_folder, email_doc_id=None):
        """Moves the email to the destination folder"""
        folder_id = self.get_folder_id(destination_folder)

        if not folder_id:
            return {
                'statusCode': 404,
                'body': json.dumps(f'Folder {destination_folder} not found')
            }

        actual_email_id = email_doc_id if email_doc_id else self.get_id_from_email(email_id)
        print(f"actual email id {actual_email_id}")
        endpoint = MOVE_EMAIL_ENDPOINT.format(user_id=self.user_id, email_id=actual_email_id)

        if self.access_token:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Prefer': 'IdType=ImmutableId'            
                }
            data = {
                "destinationId": folder_id
            }

            print("Before moving email")
            print(self.get_email_count_of_folder(folder_id))
            print(f"Moving email_id {actual_email_id} to folder {destination_folder}")
            print("Moving email to folder {}...".format(destination_folder))
            response = requests.post(endpoint, headers=headers, json=data)

            print(f"Microsoft response from move email {response}")

            if response.status_code == 201:
                print("After moving email")
                print(self.get_email_count_of_folder(folder_id))
                return {
                    'statusCode': 200,
                    'body': json.dumps(f'Email {email_id} moved to {destination_folder}')
                }
            else:
                print(f"Error moving email: {response.json()}")
                print(f"Status code: {response.status_code}")
                return {
                    'statusCode': response.status_code,
                    'body': json.dumps(f'Error moving email: {response.json()}')
                }
        else:
            return {
                'statusCode': 401,
                'body': json.dumps('Unauthorized: Unable to get access token')
            }
        
    def get_email_count_of_folder(self, folder_id):
        """Gets the count of emails in a folder"""
        endpoint = MAIL_FOLDERS_ENDPOINT.format(user_id=self.user_id)

        if self.access_token:
            headers = {'Authorization': f'Bearer {self.access_token}'}
            response = requests.get(endpoint, headers=headers)

            if response.status_code == 200:
                folders = response.json().get('value', [])
                for folder in folders:
                    if folder['id'] == folder_id:
                        return folder['totalItemCount']
            else:
                print('Error fetching folders:', response.json())


    def list_folders(self):
        """Lists the email folders"""
        endpoint = MAIL_FOLDERS_ENDPOINT.format(user_id=self.user_id)

        if self.access_token:
            headers = {'Authorization': f'Bearer {self.access_token}'}
            response = requests.get(endpoint, headers=headers)

            if response.status_code == 200:
                folders = response.json().get('value', [])
                folder_list = [{'name': folder['displayName'], 'id': folder['id']} for folder in folders]
                return {
                    'statusCode': 200,
                    'body': json.dumps(folder_list)
                }
            else:
                return {
                    'statusCode': response.status_code,
                    'body': json.dumps(f'Error fetching folders: {response.json()}')
                }
        else:
            return {
                'statusCode': 401,
                'body': json.dumps('Unauthorized: Unable to get access token')
            }

    def list_emails_from_folder(self, folder_name):
        """Lists all emails from a specific folder with pagination support"""
        folder_id = self.get_folder_id(folder_name)

        if not folder_id:
            return {
                'statusCode': 404,
                'body': json.dumps(f'Folder {folder_name} not found')
            }

        endpoint = FOLDER_MESSAGES_ENDPOINT.format(user_id=self.user_id, folder_id=folder_id)

        if self.access_token:
            headers = {'Authorization': f'Bearer {self.access_token}'}
            email_list = []
            while endpoint:  # Loop until no more pages are available
                response = requests.get(endpoint, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    emails = data.get('value', [])
                    email_list.extend([{'subject': email['subject'], 'id': email['id']} for email in emails])

                    # Check if there's a next link for more pages of emails
                    endpoint = data.get('@odata.nextLink')
                else:
                    return {
                        'statusCode': response.status_code,
                        'body': json.dumps(f'Error fetching emails: {response.json()}')
                    }
            return {
                'statusCode': 200,
                'body': json.dumps(email_list)
            }
        else:
            return {
                'statusCode': 401,
                'body': json.dumps('Unauthorized: Unable to get access token')
            }
      
    def move_all_emails_from_folder(self, source_folder, destination_folder):
        """
        Moves all emails from the source folder to the destination folder.
        """
        # Get the emails from the source folder
        emails_response = self.list_emails_from_folder(source_folder)
        emails_data = json.loads(emails_response['body'])  # Load the email data from the response body
        
        if emails_response['statusCode'] != 200 or len(emails_data) == 0:
            return {
                'statusCode': emails_response['statusCode'],
                'body': json.dumps(f'No emails to move from {source_folder} or error fetching emails')
            }
        
        # Move each email to the destination folder
        for email in emails_data:
            email_id = email['id']
            move_response = self.move_email(email_id, destination_folder)

            if move_response['statusCode'] != 200:
                # Log the error if an email cannot be moved
                print(f"Error moving email {email_id}: {move_response['body']}")
        
        return {
            'statusCode': 200,
            'body': json.dumps(f'All emails from {source_folder} moved to {destination_folder}')
        }

    def move_n_emails_from_folder(self, n, source_folder, destination_folder):
        """
        Moves the specified number of emails from the source folder to the destination folder.
        
        Args:
            n (int): The number of emails to move.
            source_folder (str): The name of the source folder.
            destination_folder (str): The name of the destination folder.
        """
        # Get emails from the source folder
        emails_response = self.list_emails_from_folder(source_folder)
        emails_data = json.loads(emails_response['body'])  # Load email data
        
        if emails_response['statusCode'] != 200 or len(emails_data) == 0:
            return {
                'statusCode': emails_response['statusCode'],
                'body': json.dumps(f'No emails to move from {source_folder} or error fetching emails')
            }
    
        # Only move up to 'n' emails
        emails_to_move = emails_data[:n]
    
        # Move each email to the destination folder
        for email in emails_to_move:
            email_id = email['id']
            move_response = self.move_email(email_id, destination_folder)
    
            if move_response['statusCode'] != 200:
                # Log the error if an email cannot be moved
                print(f"Error moving email {email_id}: {move_response['body']}")
        
        return {
            'statusCode': 200,
            'body': json.dumps(f'{len(emails_to_move)} emails moved from {source_folder} to {destination_folder}')
        }
