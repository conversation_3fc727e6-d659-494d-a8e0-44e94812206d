import os
import json
import traceback
from bre_files.pre_inventory_invoices_engine.stores.porsche_bre_engine import PorscheInvoicesBreEngine
from utils.boto3_utils            import get_secret

def lambda_handler(event, context):

    # Retrieve input data
    print(event)
    
    if event is None or event is {}:
        raise ValueError("Event empty. Skipping...")
    
    mongo_uri = get_secret(f'{os.environ["ENV"]}-mongodb_uri', False)


    document = event.get('document', {})
    ocr_groups = document.get('ocr_groups', [])
    if not ocr_groups:
        raise Exception('No groups found in document')
    
    bre = None
    if "invoices_pre_inventory" in ocr_groups[0]:
        bre = PorscheInvoicesBreEngine(event, mongo_uri)

    bre_response = bre.run()

    print("********** BRE RESPONSE **********")
    print(bre_response)
    print("*********************************")
    
    return bre_response


        

        



    