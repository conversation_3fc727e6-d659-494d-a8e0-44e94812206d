import json
import traceback
from mongo_wrapper import MongoWrapper
from boto3_utilities import get_secret
import os

def lambda_handler(event, context):
   """
   ################# CREATE #################
   create a single item
   {
      "action": "insert_one",
      "tags": ["tag1"],
      "key": "item_key",
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": {"key1": "value1"}
   }

   create multiple items
   {
      "action": "insert_many",
      "tags": ["tag1","tag2"],
      "key": ["key1", "key2"],
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": [{"key1": "value1"},{"key2": "value2"}]
   }

   
   ################# GET #################
   get a single item
   {
      "action": "get_one",
      "lock_item": true|false,
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": {"key1": "value1"}
   }

   get multiple items
   {
      "action": "get_many",
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": {"key1": "value1"}
   }   

   
   ################# UPDATE #################
   update a single item
   {
      "action": "update_one",
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": {"$set": {"key1": "value2"}},
      "filter": {"key1": "value1"}
   }

   update multiple items
   {
      "action": "update_many",
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": {"$set": {"key1": "value2"}},
      "filter": {"key1": "value1"}
   }


   ################# DELETE #################
   delete a single item
   {
      "action": "delete_one",
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": {"item_key":"cacatua_1"}
   }

   delete multiple items
   {
      "action": "delete_many",
      "database_name": "db_name", 
      "collection_name": "collection_name",
      "data": {"item_key":"cacatua_1"}
   }   
    
   """      
   try:
      # Extracting event data
      print(event)
      
      event = json.loads(event['body'])
      action = event.get('action','')
      if action not in ['insert_one', 'insert_many', 'get_one', 'get_many', 'update_one', 'update_many', 'delete_one', 'delete_many', 'get_count']:
         raise Exception(f'Action not supported: {action}')
      database_name = event.get('database_name','')
      collection_name = event.get('collection_name','')
      data = event.get('data',{})
      filter = event.get('filter',{})
      sort_field = event.get('sort_field', 'item_creation')
      lock_item = event.get('lock_item', False)
      limit = event.get('limit', 0)
      tags = event.get('tags', [])
      key = event.get('key', '')
      if not database_name or not collection_name:
         raise Exception(f'DB/collection not found: {database_name}|{collection_name}')

      # First, extract the mongo_uri from secrets manager
      mongo_uri = get_secret(f'{os.environ["ENV"]}-mongodb_uri', False)


      # Perform action
      if action in ['insert_one', 'insert_many']:
         result = MongoWrapper(mongo_uri, database_name, collection_name).create_item(action, data, tags, key)
      elif action in ['get_one', 'get_many', 'get_count']:
         result = MongoWrapper(mongo_uri, database_name, collection_name).get_item(action, data, lock_item, sort_field, limit)
      elif action in ['update_one', 'update_many']:
         result = MongoWrapper(mongo_uri, database_name, collection_name).update_item(action, values=data, filter=filter) 
      elif action in ['delete_one', 'delete_many']:
         result = MongoWrapper(mongo_uri, database_name, collection_name).delete_item(action, values=data)
      
      return {
         'statusCode': 200,
         'body': json.dumps(result, default=str) if not isinstance(result, str) else result
      }
   except Exception as e:
      print('Issue while processing: {}'.format(traceback.format_exc()))
      return {
         'statusCode': 500,
         'body': json.dumps('NOK!')
      }