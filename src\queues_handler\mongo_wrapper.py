from datetime import datetime
from mongo_utils import Mongo

class MongoWrapper():
    def __init__(self, mongo_uri, db_name, collection_name):
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(db_name, collection_name)

    def normalize_input(self, values, key, tags):
        """
        Method that normalizes the items added to the queue to follow this structure:
        {
            "item_key": ,
            "item_status": "pending",
            "item_exception_reason": "",
            "item_attempt": 0,
            "item_tags": ,
            "item_data": values, 
            "item_creation": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
            "item_modified": "",
            "item_processing_time": 0.00
        }
        """
        if isinstance(values, dict):
            output = {
                        "item_key": key,
                        "item_status": "pending",
                        "item_exception_reason": "",
                        "item_attempt": 0,
                        "item_tags": tags,
                        "item_data": values, 
                        "item_creation": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
                        "item_modified": "",
                        "item_processing_time": 0.00
                    }  
        else:
            output = []
            c = 0
            for item in values:
                output.append({
                    "item_key": key[c],
                    "item_status": "pending",
                    "item_exception_reason": "",
                    "item_attempt": 0,
                    "item_tags": tags[c],
                    "item_data": item, 
                    "item_creation": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
                    "item_modified": "", 
                    "item_processing_time": 0.00
                })
                c+=1
        return output

    def create_item(self, action, values, tags, key):
        try:
            if action=='insert_one':
                self.mongo.insert_one(data=self.normalize_input(values, key, tags))
            elif action=='insert_many':
                self.mongo.insert_many(data=self.normalize_input(values, key, tags))
            return True
        except Exception as e:
            print(f'Error: {e}')
            return False

    def get_item(self, action, values, lock_item, sort_field, limit):
        try:
            r = []
            print(self.mongo.count_documents(values))
            if action=='get_one':
                if lock_item:
                    r = [self.mongo.find_one_and_update(filter=values, data={'$set': {'item_status': 'locked', 'item_modified': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}})]
                else:
                    r = [self.mongo.find_one(data=values)]
            elif action=='get_many':
                r = list(self.mongo.find(data=values, sort_field=sort_field, limit=limit))
            elif action=='get_count':
                r = self.mongo.count_documents(values)
            return r
        except Exception as e:
            print(f'Error: {e}')
            return []

    def update_item(self, action, values, filter):
        try:
            if not values.get('$set', ''):
                values['$set'] = {'item_modified': datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}
            else:
                values['$set']['item_modified'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
            print(self.mongo.count_documents(filter))
            if action=='update_one':
                self.mongo.update_one(filter=filter, data=values)
            elif action=='update_many':
                self.mongo.update_many(filter=filter, data=values)
            return True
        except Exception as e:
            print(f'Error: {e}')
            return False

    def delete_item(self, action, values):
        try:
            print(self.mongo.count_documents(values))
            if action=='delete_one':
                self.mongo.delete_one(data=values)
            elif action=='delete_many':
                self.mongo.delete_many(data=values)
            return True
        except Exception as e:
            print(f'Error: {e}')
            return False