
from boto3_utils import download_file_from_s3, trigger_lambda
import os
import pandas as pd
import json
from crud_report_rows import CrudReynolsReport as CrudVins
import time
    
def return_file_name_by_store(store):
    """
    Returns the file name for the new vehicles report based on the store.
    """
    file_name = ""
    if store in ["PNW", "POR"]:
        file_name = f"new_vehicles_{store.upper()}.xls"

    return file_name

def load_porsche_vins_in_db(store, local_path, crud_vins: CrudVins):
    """
    Loads the VINs from the local file into the database for Porsche stores.
    """
    error_vins = []

    # Read the Excel file
    tables = pd.read_html(local_path)
    df = tables[0]
    df.columns = df.iloc[0]
    df = df.drop(index=0).reset_index(drop=True)

    # 2. Remove ='...' wrapping (regex based clean)
    for col in df.columns:
        df[col] = df[col].map(lambda x: str(x).strip().replace('="', '').replace('"', '') if isinstance(x, str) else x)

    # Process each row and insert into the database
    for index, row in df.iterrows():
        vin = row['Vehicle Identification Number']
        # Here you would insert the logic to save the VIN to your database
        print(f"Inserting VIN: {vin} for store: {store}")
        
        row["VIN"] = vin
        row["STORE"] = store
        row["MAKE"] = "PORSCHE"
        row["MODEL CODE"] = row.get('Model Type', "")
        row["MODEL DESCRIPTION"] = row.get('Model Descr.', "")
        row["COMMISSION NUMBER"] = row.get('Commission Number', "")
        row["COLOR"] = row.get('Body Color', "")

        vin_data = crud_vins.find_report_row_by_vin(vin)
        if vin_data:
            if vin_data["current_flow"] != "pre-inventory":
                error_vins.append(vin)
                print(f"VIN {vin} already exists in the database with a different flow: {vin_data['current_flow']}")
            else:
                crud_vins.update_row_pre_inventory(row, local_path.split("/")[-1])
        else:
            crud_vins.insert_row_pre_inventory(row, local_path.split("/")[-1])
    
    return error_vins


def load_vins_in_db(store, local_path):
    """
    Loads the VINs from the local file into the database.
    """
    print(f"Loading VINs for store {store} from {local_path}")

    crud_vins = CrudVins()
    
    error_vins = []
    if store in ["PNW", "POR"]:
        error_vins = load_porsche_vins_in_db(store, local_path, crud_vins)


    return error_vins

def lambda_handler(event, context):

    try:

        store = event.get("store", None)
        if store is None or store == "":
            return {
                'statusCode': 500,
                'body': {
                    "message": json.dumps(f"Error no store provided!")
                }
            }
        
        print(" ****** DOWNLOADING REPORT ****** ")

        todays_path = time.strftime('%Y/%m/%d')

        file_name = return_file_name_by_store(store)
        local_path = f"/tmp/{file_name}"
        s3_url = f"s3://{os.environ['BUCKET']}/{os.environ['NEW_VEHICLES_REPORTS_FOLDER']}/{todays_path}/{file_name}"
        
        downloaded = download_file_from_s3(s3_url=s3_url, local_path=local_path)
      
        if downloaded == False:
            print("Failed to download the file from S3")
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "Failed to download the file from S3"})
            }
        
        
        error_vins = load_vins_in_db(store, local_path)

        if len(error_vins) > 0:
            trigger_lambda(os.environ['REPORT_TO_ARIA_LAMBDA'], {"body": {"vins": error_vins, "type": "wrong_flow", "stage": "pre-inventory"}})


    except Exception:
        return {
            "statusCode": 500,
            "body": json.dumps({"message": "Report couldnt be processed correctly"})
        }

    return {
        "statusCode": 200,
        "body": json.dumps({"message": "Report processed correctly"})
    }


    

    
    
    
