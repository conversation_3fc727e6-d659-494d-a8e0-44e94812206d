import os
from PyPDF2         import PdfReader
from crud_emails    import CrudEmails
from crud_bols    import CrudBols
from crud_invoices import CrudInvoices
from aria_utils     import AriaUtils
from datetime import datetime
import requests
from textract import TextractResponseHandler

class EmailProcesser():
    def __init__(self, logger_class, email_id, stage):
        self.env = os.environ['ENV']
        self.bucket = os.environ['BUCKET']
        
        self.aria_utils = AriaUtils(app_id=self.app_id)
        self.crud_emails = CrudEmails()
        self.crud_bols = CrudBols()
        self.crud_invoices = CrudInvoices()
        self.textract_handler = TextractResponseHandler()

        self.email = self.crud_emails.find_email_by_id(email_id)
        self.attachments = self.email['attachments']
        self.stage = stage
        self.logger_class = logger_class
        self.logger = self.logger_class.get_root_logger()


    def is_pdf(self, attachment_name):
        """
        This function checks if the attachment is a PDF file.
        """
        return attachment_name.lower().endswith('.pdf')

    def is_pdf_digital(self, pdf_path):
        """
        This function checks if the PDF is digital.
        """
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            for page_num in range(len(reader.pages)):
                page = reader.pages[page_num]
                # If any page has text, return true
                if page.extract_text():
                    return True
            return False
        
    def is_pdf_url(self, url):
        """Check if the URL is a PDF."""
        if url.endswith(".pdf"):
            return True
        
        try:
            response = requests.head(url, allow_redirects=True, timeout=5)
            content_type = response.headers.get("Content-Type", "").lower()
            return "application/pdf" in content_type
        except requests.RequestException:
            return False
    
    def download_pdf(self, url):
        """Download the PDF file."""
    
        try:
            response = requests.get(url)  # Send request
            if response.status_code == 200:
                filename = url.split("/")[-1]
                return filename, response.content
            else:
                print(f"Failed to download: {url}")
        except requests.RequestException as e:
            print(f"Error: {e}")
        
        return None
    
    def generate_s3_path(self, folder, id = None):
        today = datetime.today()
        year = today.strftime("%Y") 
        month = today.strftime("%m") 
        day = today.strftime("%d") 
        
        if id != None:
            s3_path = f"{folder}/{year}/{month}/{day}/{id}"
        else:
            s3_path = f"{folder}/{year}/{month}/{day}"

        return s3_path

        
    def run(self):
        pass

    def process_attachments(self):
        pass