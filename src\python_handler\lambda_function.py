import json
import traceback
from process_wrapper import ProcessWrapper


def lambda_handler(event, context):
   """
   ################# LAUNCH #################
   launch process downloading repo
   {
   "action": "launch_process",
   "gitlab_force_download": true,
   "gitlab_repo_url": "alvaroq%2Frpa_dummy_test",
   "gitlab_branch": "test",
   "gitlab_token": "glpat-dummytoken",
   "instance_id": "ec2_machine_id",
   "working_dir": "C:\\tmp",
   "script_name": "path\\to\\script\\script.py",
   "user": "system",
   "force_cleanup": false,
   "task_name": "",
   "script_args": "--param1 value1 --param2 value2"
   }
   Output: 
   {
      "StandardOutputContent": "SUCCESS: The scheduled task \"RunPythonScript_18_45_09_762569\" has successfully been created.\r\nSUCCESS: Attempted to run the scheduled task \"RunPythonScript_18_45_09_762569\".\r\n", 
      "StandardErrorContent": ""
   }

   ################# UPDATE #################
   update a secret with a str value
   {
      "action": "check_process",
      "instance_id": "ec2_machine_id",
      "task_name": "RunPythonScript_18_45_09_762569"
   }
   Output:
   {
      "StandardOutputContent": "Status:                               Running\r\n", "StandardErrorContent": ""
   }
   """
   try:
      print(event)
      # Extract event data
      body = event.get('body')
 
      if isinstance(body, str):
         event = json.loads(body)
      elif isinstance(body, dict):
         event = body
      else:
         raise ValueError("Unsupported body format in event.")
      
      action = event.get('action','')
      gitlab_force_download = event.get('gitlab_force_download', False)
      gitlab_repo_url = event.get('gitlab_repo_url','')
      gitlab_branch = event.get('gitlab_branch','')
      gitlab_token = event.get('gitlab_token','')
      instance_id = event.get('instance_id','')
      working_dir = event.get('working_dir','')
      script_name = event.get('script_name','')
      user = event.get('user','')
      force_cleanup = event.get('force_cleanup', False)
      task_name = event.get('task_name')
      
      # New: Extract script arguments to pass to the Python script
      script_args = event.get('script_args', '')
      
      # Perform action
      output = []
      if action == 'launch_process':
         if gitlab_force_download:
            output.append(ProcessWrapper().download_repo(gitlab_repo_url, gitlab_token, gitlab_branch, instance_id, working_dir))
            output.append(ProcessWrapper().install_dependencies(instance_id, working_dir))
         # Pass the new script_args parameter here
         output.append(ProcessWrapper().launch_process(instance_id, working_dir, script_name, user, script_args))
         if force_cleanup:
            output.append(ProcessWrapper().cleanup(instance_id, working_dir))
      elif action == 'check_process':
         output.append(ProcessWrapper().check_process(instance_id, task_name))
      
      return {
         'statusCode': 200,
         'body': json.dumps(output) if output else json.dumps('OK!')
      }
   except Exception as e:
      print('Issue while processing: {}'.format(traceback.format_exc()))
      return {
         'statusCode': 500,
         'body': json.dumps('NOK!')
      }