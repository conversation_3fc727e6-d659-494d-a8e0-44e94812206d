import boto3
import time
from botocore.exceptions import ClientError


class Boto3Utilities():
    def __init__(self):
        self.ssm_client = boto3.client('ssm')
        
    def send_ssm_command(self, instance_id, commands):
        try:
            response = self.ssm_client.send_command(
                        InstanceIds=[instance_id],
                        DocumentName="AWS-RunPowerShellScript",
                        Parameters={"commands": commands},
                        TimeoutSeconds=300,
                        Comment=""
                    )
            command_id = response['Command']['CommandId']
            return command_id
        except Exception as e:
            print(f'Error: {e}')
            return ''
        
    def get_ssm_command_output(self, command_id, instance_id, max_retries=50, retry_delay=2):
        try:
            retries = 0
            while retries < max_retries:
                time.sleep(retry_delay)
                try:
                    result = self.ssm_client.get_command_invocation(
                        CommandId=command_id, 
                        InstanceId=instance_id
                    )
                    status = result['Status']
                    if status in ["Success", "Failed", "Cancelled", "TimedOut"]:
                        return {
                            "StandardOutputContent": result.get('StandardOutputContent', ''),
                            "StandardErrorContent": result.get('StandardErrorContent', '')
                        }
                except ClientError as e:
                    if "InvocationDoesNotExist" in str(e):
                        print(f"Command invocation not yet registered. Retrying in {retry_delay} seconds...")
                    else:
                        raise e

                retries += 1
                time.sleep(retry_delay)
            raise Exception(f"Max retries reached while waiting for SSM command {command_id} to complete.")
        
        except Exception as e:
            print(f'Error: {e}')
            return ''
