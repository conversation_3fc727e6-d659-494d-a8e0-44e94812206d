import os
import datetime

from utils.mongo_utils import Mongo
from utils.boto3_utils import get_secret


class CrudBols:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name='bol'

    def insert_bol(self, email_id, attachment_id, attachment_name, path, file_ocr, wi_id):


        bol_document = {
            "email_id": email_id,
            "attachment_name": attachment_name,
            "attachment_id": attachment_id,
            "path": path,
            "raw_ocr": file_ocr,
            "aria_wi_id": wi_id,
            "read_at": datetime.datetime.now(),
            "status_history": ["Pending"]
        }
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.insert_one(bol_document)

    def update_bol_by_wi_id(self, wi_id, data_to_set, data_to_push):
        query = {"aria_wi_id": wi_id}
        data_to_set['updated_at'] = datetime.datetime.now()
        data = {"$set": data_to_set, "$push": data_to_push} 
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.update_one(query, data)


    def find_bol_by_id(self, attachment_id):
        """
        This function finds a bol by its attachment ID.
        """
        query = {"attachment_id": attachment_id}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def find_bol_by_wi_id(self, vin):
        query = {"aria_wi_id": vin}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def find_bol_by_email_id(self, bol_id):
        """
        This function finds a bol by its ID.
        """
        query = {"aria_wi_id": bol_id}
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)

    def find_bols_by_filter(self, filter):
        """
        This function finds bols by a filter.
        """
        self.mongo.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return list(self.mongo.find(filter))
    
    def __del__(self):
        self.mongo.close_connection()