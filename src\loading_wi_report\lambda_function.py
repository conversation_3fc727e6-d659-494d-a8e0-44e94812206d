import os
import json
import requests
from outlook_utils import Outlook
from boto3_utils import get_aria_credentials, get_parameter
from aria_utils import AriaClient

def lambda_handler(event, context):
    """
    Lambda function to query multiple ARIA apps for work items
    """

    print(event)

    stage = event.get("stage")

    if stage is None or stage == "":
        return {
            'statusCode': 500,
            'body': {
                "message": json.dumps(f"Error no stage provide!")
            }
        }
    

    selected_app = event.get("app", None)

    aria_auth = get_aria_credentials()
    token = aria_auth.get('token')

    if not token:
        print("Error: Unable to retrieve the authentication token.")
        return {'statusCode': 500, 'body': "Error: Unable to retrieve the authentication token."}

    params = get_parameter(f"{os.environ['ENV']}-report_data_ids", return_json=True)
    apps = params[f"{os.environ['ENV']}-report_data_ids"][stage]
    
    processed_workitems = {}
    headers = {"Authorization": f"{token}", "Content-Type": "application/json"}

    if selected_app is not None:
        apps = {k: v for k, v in apps.items() if k in [selected_app]}

    for app_name, app_data in apps.items():
        if not all(app_data.values()):
            print(f"Skipping {app_name} due to missing configuration.")
            continue

        aria_client = AriaClient(app_id=app_data["app_id"], app_uuid=app_data["app_uuid"])
        workitems_url = f"{aria_client.aria_base_url}/work_items?filter[{app_data['status_column']}]={app_data['loading_uuid']}"
        
        print("ASKING TO ", workitems_url)

        try:
            response = requests.get(workitems_url, headers=headers)
            print(f"{app_name} Response: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json().get("data", [])
                    processed_workitems[app_name] = [
                        {"id": item["id"], "workitem_link": aria_client.get_aria_workitem_url(item["id"])}
                        for item in data
                    ]
                except json.JSONDecodeError:
                    print(f"Error: JSON decode issue for {app_name}.")
            elif response.status_code == 204:
                print(f"No work items found for {app_name}.")
                processed_workitems[app_name] = []
            else:
                print(f"Error fetching {app_name} work items: {response.status_code} - {response.text}")
        
            print(processed_workitems)

        except requests.exceptions.RequestException as e:
            print(f"Request failed for {app_name}: {str(e)}")
            processed_workitems[app_name] = []
    
    # Send email if there are any processed work items
    if any(processed_workitems.values()):
        try:
            env = os.environ.get('ENV')
            outlook = Outlook()
            support_emails = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
            copy_emails = [email.strip() for email in os.environ.get('BCC_EMAIL', '').split(',')]
            sender = os.environ.get('REPORTER_EMAIL')
            
            subject = f"{env.upper()} - ARIA Work Item Processing Report"
            aria_url = aria_client.aria_url
            body = outlook.generate_email_body_html(processed_workitems, aria_url)
            
            outlook.send_email_notification(subject=subject, body=body, emails={
                "sender": sender,
                "recipients": support_emails,
                "bcc": copy_emails
            })
        except Exception as e:
            print(f"Error sending email: {e}")
            return {'statusCode': 500, 'body': json.dumps({'message': f"Error sending email: {e}"})}
    
    return {'statusCode': 200, 'body': json.dumps({"Processed Workitems": processed_workitems})}
