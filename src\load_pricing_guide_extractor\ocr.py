import time
from enum import Enum
import boto3
from botocore.client import Config
"""
This class contains all the related to the OCR operations
that we need to process a document in Textract
"""

config = Config(
    retries={
        'max_attempts': 30,  # Number of retry attempts
        'mode': 'standard'  # Retry mode (standard or adaptive)
    }
)


class OCRStatus(Enum):
    pending = 0
    completed = 1
    failed = 2
    not_applicable = 3
    retry = 4
    initial_failure = 5


class Ocr():

    def __init__(self, textract_adapter_details):
        
        self.textract_adapter_details = textract_adapter_details

        self.s3 = boto3.client(
            's3',
            config=Config(signature_version='s3v4')
        )        
        self.textract = boto3.client(
            'textract',
            config=config
        )

    def async_call_textract(self, file_name, file_content):
        """
        Here we send the document to textract and return
        the job id generated on the textract api and the
        ocr status set to pending (still to be processed)
        """

        folder_name = self.textract_adapter_details['bucket_folder']
            
        document_location = f'{folder_name}/{file_name}'
        self.s3.put_object(Body=file_content, Bucket=self.textract_adapter_details['bucket_name'], Key=document_location)
        # Full page OCR
        
        textract_response = self.textract.start_document_text_detection(
            DocumentLocation={
                'S3Object' : {
                    'Bucket' : self.textract_adapter_details['bucket_name'],
                    'Name' : document_location
                }
            }
        )
        
        job_id = textract_response['JobId']

        if not job_id:
            raise Exception("Job initialization failed.")

        return job_id, OCRStatus.pending.value
    
    def get_response(self, job_id, next_token = ""):
        """
        Here we try to ask for the ocr status of the job id
        of the document, to see if there is a new status
        and how it goes
        """

        if next_token != "":
            #job_id_response = self.textract.get_expense_analysis(JobId=job_id, NextToken=next_token)
            job_id_response = self.textract.get_document_text_detection(JobId=job_id, NextToken=next_token)
        else:
            #job_id_response = self.textract.get_expense_analysis(JobId=job_id)
            job_id_response = self.textract.get_document_text_detection(JobId=job_id)


        job_status = job_id_response.get("JobStatus", "")
        next_token = job_id_response.get("NextToken", "")
        
        ocr_status = OCRStatus.pending.value

        if job_status == "SUCCEEDED":
            ocr_status = OCRStatus.completed.value
        elif job_status == "PARTIAL_SUCCESS":
            ocr_status = OCRStatus.retry.value
        elif job_status == "FAILED":
            ocr_status = OCRStatus.failed.value

        return ocr_status, job_id_response, next_token

    
    def get_full_page_string(self, textract_response, only_string = False):
        """
        Given the textract response we fill the complete_document_text
        only with the word block that are contained in the textract
        response
        """

        just_blocks = []
        word_blocks = []
        
        mega_string = ""
        for response in textract_response:
            if response.get('ExpenseDocuments', ''):
                for expense in response['ExpenseDocuments']:
                    just_blocks.extend(expense['Blocks'])
            else:
                just_blocks = response['Blocks']

            
            max_page = 0
            idx = 0
            
            for block in just_blocks:
                if block['BlockType'] == 'WORD':

                    mega_string += block['Text'] + " "
                    word_data = {
                        "word": block['Text'],
                        "page": block['Page'],
                        #"Id": idx#block['Id'],
                    }

                    idx += 1

                    #if int(block['Page']) > max_page:
                    #    max_page = int(block['Page'])
                    

                    word_blocks.append(word_data)

        print(mega_string)
        return word_blocks, mega_string
    