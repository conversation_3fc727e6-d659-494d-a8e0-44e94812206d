from textract import TextractResponseHandler
from boto3_utils import download_file_from_s3, trigger_lambda_response, get_secret
import base64
import os
import json
import traceback
from prompts import prompt, message_extraction, message_get_model_codes, column_names
import pymongo

def construct_get_model_codes_pricing_guide_page(provider, host, llm_model, document_ocr):
    return {
        "provider": provider,#"bedrock",
        "host": host,#"claude",
        "llm_model": llm_model,#"claude3.7-sonnet",
        "prompt": prompt,
        "message": f"""
            
            {message_get_model_codes}

            {document_ocr}
        
        """
    }


def extract_model_codes(document_ocr):

    llm_vals = {
        "gpt": {
            "provider": "openai",
            "host": "public",
            "model": "gpt4o"
        },
        "claude3.7-sonnet": {
            "provider": "bedrock",
            "host": "claude",
            "model": "claude3.7-sonnet"
        },
        "claude4-sonnet": {
            "provider": "bedrock",
            "host": "claude",
            "model": "claude4-sonnet"
        },
    }

    for k, v in llm_vals.items():
        retry = 3
        correctly_finished_llm = False
        while (retry > 0 and correctly_finished_llm == False):
            msg_to_extract_information =  construct_get_model_codes_pricing_guide_page(v["provider"], v["host"], v["model"], document_ocr)
            llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_extract_information)
            llm_lambda_response = json.loads(llm_lambda_response)
            print(f" ****** RESPONSE FROM LLM {v['model']} ****** ")
            print(llm_lambda_response)
            print("********************************************")

            if llm_lambda_response['statusCode'] != 200:
                retry -= 1
            else:
                correctly_finished_llm = True
            
            llm_lambda_response = json.loads(llm_lambda_response['body'])
            model_codes = llm_lambda_response['message'].strip()

            model_codes = json.loads(model_codes[model_codes.find("{"):model_codes.rfind("}") + 1])
            print("Model codes found: ", model_codes)
            llm_vals[k]["model_codes"] = model_codes["model_codes"]
    
    if set(llm_vals["gpt"]["model_codes"]) == set(llm_vals["claude3.7-sonnet"]["model_codes"]) == set(llm_vals["claude4-sonnet"]["model_codes"]):
        return llm_vals["gpt"]["model_codes"]  # They're all the same
    else:
        # Merge and remove duplicates
        return list(set(llm_vals["gpt"]["model_codes"] + llm_vals["claude3.7-sonnet"]["model_codes"] + llm_vals["claude4-sonnet"]["model_codes"]))

def construct_get_data_from_pricing_guide_page_table(image_uri, document_ocr, model_codes):
    return {
            "provider": "openai",
            "host": "public",
            "llm_model":"gpt-4o-vision",
            "prompt": prompt,
            "message": f"""
                
                {message_extraction}
                
                The ocr of the image is the next, ensure using the OCR that is the plaint text of the entire file: 
                {document_ocr}
                
                The model codes inside this document are this:
                {model_codes}
            
            """,
            "files": [image_uri]
        }

def lambda_handler(event, context):

    print(event)

    store = event.get("store", None)
    if store is None:
        return {"statusCode": 400, "body": json.dumps({"error": "Store didnt come in the event...Skipping..."})}
    
    event = event.get("items", {})
    pdf_file_to_process = event.get("splitted_page", None)
    img_file_to_process = event.get("splitted_img", None)

    if not pdf_file_to_process or not img_file_to_process:
        return {
            "statusCode": 500,
            "body": json.dumps({"message": "Not page or img to process...Skipping..."})
        }

    textract_handler = TextractResponseHandler("/tmp/")

    mongo_uri = get_secret(f"{os.environ['ENV']}-mongodb_uri", return_json = False)
    client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
    db = client[os.environ['MONGO_DATABASE']]
    pricing_sheet_collection = db.get_collection(f"{store.lower()}_pricing_sheet")

    try:

        try:
            file_name_pdf = pdf_file_to_process.split("//")[-1].split("/")[-1]
            download_file_from_s3(pdf_file_to_process, f"/tmp/{file_name_pdf}")
            
        except Exception:
            print(traceback.format_exc())
            return {
                "statusCode": 500,
                "body": json.dumps({"message": "PDF File couldnt be downloaded...Skipping..."})
            }

        documents_text = {}
        batch_documents_text = textract_handler.get_complete_text_of_files([file_name_pdf])
        documents_text.update(batch_documents_text)

        
        file_name_img = img_file_to_process.split('//')[-1].split('/')[-1]

        print("Processing img: ", file_name_img)
        print("Processing file: ", file_name_pdf)
        print("Page:", file_name_img.split('_')[-1].split('.')[0])
        print("Extracted ocr", documents_text[file_name_pdf][:100])

        ocr_retry = 3
        while documents_text[file_name_pdf] == "" and ocr_retry > 0:
            batch_documents_text = textract_handler.get_complete_text_of_files([file_name_pdf])
            documents_text.update(batch_documents_text)
            ocr_retry -= 1

        correctly_finished_llm_resposne = False
        retry = 3
        while (not correctly_finished_llm_resposne and retry > 0):
            
            model_codes = extract_model_codes(documents_text[file_name_pdf])

            msg_to_extract_information =  construct_get_data_from_pricing_guide_page_table(img_file_to_process, documents_text[file_name_pdf], model_codes)
            cols = column_names[store]
            for key, value in cols.items():
                msg_to_extract_information["message"] = msg_to_extract_information["message"].replace(key, value)

            llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_extract_information)
            llm_lambda_response = json.loads(llm_lambda_response)
            print(f" ****** RESPONSE FROM LLM ****** ")
            print(llm_lambda_response)
            print("********************************************")

            if llm_lambda_response['statusCode'] != 200:
                retry -= 1
                continue
            else:
                correctly_finished_llm_resposne = True
            
            llm_lambda_response = json.loads(llm_lambda_response['body'])
            llm_lambda_response = llm_lambda_response['message']

            print(llm_lambda_response)
            models_pricing_guide = {}
            models_pricing_guide = llm_lambda_response
            if isinstance(llm_lambda_response, str):
                models_pricing_guide = json.loads(models_pricing_guide) if not "json" in str(models_pricing_guide) else json.loads(models_pricing_guide[models_pricing_guide.find("{"): models_pricing_guide.rfind("}") + 1])

            
            for key in list(models_pricing_guide.keys()):
                rows = models_pricing_guide[key]
                for row in rows:

                    # We look for older row with same model code to delete it to try avoiding deleting it
                    # if the llm had a problem
                    older_row = pricing_sheet_collection.find_one({"model_code": key, "erasable": True})
                    pricing_sheet_collection.delete_one({"_id": older_row["_id"]}) if older_row is not None else None

                    # we insert the new row
                    pricing_sheet_collection.insert_one({"model_code": key, "model_name": row["Model name"], "MSRP": row["MSRP"], "Dlr Inv.": row["Dlr Inv."], column_names[store]["FIELD_A"]: row[column_names[store]["FIELD_A"]], column_names[store]["FIELD_B"]: row[column_names[store]["FIELD_B"]], column_names[store]["FIELD_C"]: row[column_names[store]["FIELD_C"]], column_names[store]["FIELD_D"]: row[column_names[store]["FIELD_D"]], "Model description": row["Model description"]})

                print(f"Model Code {key} has been updated!")
        
        if correctly_finished_llm_resposne == False:
            return {
                "statusCode": 500,
                "body": json.dumps({"message": "Error response from llm."})
            }


    except Exception:
        print("Exception: ", traceback.format_exc())
        return {
            "statusCode": 200,
            "body": json.dumps({"error": f"Honda file {file_name_pdf} couldnt be loaded successfully."})
        }

    return {
        "statusCode": 200,
        "body": json.dumps({"message": f"Honda file {file_name_pdf} loaded successfully."})
    }