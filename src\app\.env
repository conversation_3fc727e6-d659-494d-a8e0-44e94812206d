# Database Configuration
DB_TYPE=mongodb
DB_HOST=localhost
DB_PORT=27017
DB_NAME=tag_title
DB_USER=
DB_PASSWORD=

# MongoDB Specific
MONGO_URI=mongodb://localhost:27017/
MONGO_DB_NAME=tag_title
MONGO_COLLECTION_NAME=json_storage

# Alternative database configurations (uncomment as needed)
# PostgreSQL
# DB_TYPE=postgresql
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=validation_db
# DB_USER=your_username
# DB_PASSWORD=your_password

# MySQL
# DB_TYPE=mysql
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=validation_db
# DB_USER=your_username
# DB_PASSWORD=your_password

# Application Configuration
APP_ENV=development
LOG_LEVEL=INFO
OUTPUT_DIR=output
TEMP_DIR=temp

# Validation Configuration
MAX_VALIDATION_ERRORS=100
VALIDATION_TIMEOUT=30
ENABLE_CROSS_VALIDATION=true

# File Processing
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=json,xlsx,csv