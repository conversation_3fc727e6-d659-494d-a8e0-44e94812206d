actions_dict = {
    "title": {
        "submit to dms": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "title": "submit"
            }
        },
        "reevaluate": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "title": "submit"
            }
        }
    },
    "bol": {
        "save": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "bol": "submit"
            }
        },
        "reevaluate": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "bol": "submit"
            }
        },
        "reprocess": {
            "next_function": "BRE_LAMBDA",
            "request_response": False,
            "bre_type": {
                "bol": "submit"
            }
        },
        "complete manually": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "bol": "complete_manually"
            }
        }
    },

    "invoice": {
        "reevaluate": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "invoice": "submit"
            }
        },
        "save": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "invoice": "save"
            }
        },
        "review": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "invoice": "review"
            }
        },
        "inspection": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                "invoice": "inspection"
            }
        }
        ,
        "complete manually": {
            "next_function": "BRE_LAMBDA",
            "request_response": False,
            "bre_type": {
                "invoice": "manually"
            }
        },
        "reprocess": {
            "next_function": "BRE_LAMBDA",
            "request_response": False,
            "bre_type": {
                "invoice": "submit"
            }
        },
        "reprocess_completed": {
            "next_function": "BRE_LAMBDA",
            "request_response": False,
            "bre_type": {
                "invoice": "completed"
            }
        },
        "make it ready again": {
            "next_function": "BRE_LAMBDA",
            "request_response": True,
            "bre_type": {
                    "invoice": "ready_again"
                }
        }
    },

    "used_cars_invoices": {
        "reevaluate": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": True,
            "bre_type": {
                "used_cars_invoices": "submit"
            }
        },
        "save": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": True,
            "bre_type": {
                "used_cars_invoices": "save"
            }
        },
        "review": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": True,
            "bre_type": {
                "used_cars_invoices": "review"
            }
        },
        "inspection": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": True,
            "bre_type": {
                "used_cars_invoices": "inspection"
            }
        },
        "complete manually": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": False,
            "bre_type": {
                "used_cars_invoices": "manually"
            }
        },
        "restore item": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": True,
            "bre_type": {
                "used_cars_invoices": "restore_item"
            }
        },
        "discard": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": True,
            "bre_type": {
                "used_cars_invoices": "discard"
            }
        },
        "reprocess": {
            "next_function": "BRE_LAMBDA_USED_CARS",
            "request_response": False,
            "bre_type": {
                "used_cars_invoices": "submit"
            }
        },
    },

    "invoices_pre_inventory": {
        "reevaluate": {
            "next_function": "BRE_LAMBDA_PRE_INVENTORY",
            "request_response": True,
            "bre_type": {
                "invoices_pre_inventory": "submit"
            }
        },
        "save": {
            "next_function": "BRE_LAMBDA_PRE_INVENTORY",
            "request_response": True,
            "bre_type": {
                "invoices_pre_inventory": "save"
            }
        },
        "review": {
            "next_function": "BRE_LAMBDA_PRE_INVENTORY",
            "request_response": True,
            "bre_type": {
                "invoices_pre_inventory": "review"
            }
        },
        "inspection": {
            "next_function": "BRE_LAMBDA_PRE_INVENTORY",
            "request_response": True,
            "bre_type": {
                "invoices_pre_inventory": "inspection"
            }
        },
        "complete manually": {
            "next_function": "BRE_LAMBDA_PRE_INVENTORY",
            "request_response": False,
            "bre_type": {
                "invoices_pre_inventory": "manually"
            }
        },
        "reprocess": {
            "next_function": "BRE_LAMBDA_PRE_INVENTORY",
            "request_response": False,
            "bre_type": {
                "invoices_pre_inventory": "submit"
            }
        },
    }
}


