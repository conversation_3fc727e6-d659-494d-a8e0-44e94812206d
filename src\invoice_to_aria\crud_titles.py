# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os

from mongo_utils import Mongo
from boto3_utils import get_secret



class CrudTitle:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='title'
        )

    def update_title_by_wi_id(self, wi_id, data):
        query = {"aria_wi_id": wi_id}
        data = {"$set": data} 
        return self.mongo.update_one(query, data)


    def find_title_by_vin(self, attachment_id):
        """
        This function finds a bol by its attachment ID.
        """
        query = {"extracted_vins": attachment_id}
        return self.mongo.find_one(query)
    
    
    def __del__(self):
        self.mongo.close_connection()