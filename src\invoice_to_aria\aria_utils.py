import os
import time
import requests
import re

from boto3_utils import get_secret

class TemporalUnavailableException(Exception):
    """ This exception will be handled in a different way than other exceptions. """
    def __init__(self, mensaje, response=None):
        super().__init__(mensaje)
        self.response = response

class AriaUtils:
    def __init__(self, app_id):
        self.aria_env = os.environ['ARIA_ENV']
        self.credentials = self.get_credentials()
        self.app_id = app_id
        
        # Construct headers
        token = self.credentials['token']
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'{token}'
        }

    def get_credentials(self):
        """
        This function retrieves the credentials from the AWS Secrets Manager.
        """

        env = os.environ['ENV']
        credentials = get_secret(f"{env}-aria_cm_tokens")
        return credentials.get(self.aria_env)
    
    def construct_create_request_post_inventory(self, vin_data, file_content):
        """
        This function constructs the request to send a workitem to the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        app_id = self.app_id
        self.url = f"{aria_url}/public/v1/apps/{app_id}/document_processing"

        pattern = r"^(lexus|porsche|honda|ford|lincoln|cadillac|gm|mazda|jaguar|landrover)"
        text_to_get_make = vin_data['flows']["post-inventory"]["report-data"]['make'].lower().replace(" ", "")
        match = re.match(pattern, text_to_get_make)
        make_val = ""
        if match:
            make_val = match.group()

        # Construct the payload
        group_name = os.environ['GROUP_NAME_POST_INVENTORY']
        self.payload = {
            "data": {
                "type": "CaseManagement",
                "attributes": {
                   "groups": [
                       {
                            "name": group_name,
                            "content": "data:application/pdf;base64," + file_content,
                            "metadata": [
                                {
                                    "name": "make",
                                    "value": make_val.upper(),
                                },
                                {
                                    "name": "store",
                                    "value": vin_data['flows']["post-inventory"]["report-data"]['store'].replace(" ", ""),
                                },
                                {
                                    "name": "stock_number",
                                    "value": vin_data['flows']["post-inventory"]["report-data"]['stock'].replace(" ", ""),
                                },



                                {
                                    "name": "wholesale_finance_reserve",
                                    "value": "",
                                    "display": False
                                },
                                {
                                    "name": "hold_back",
                                    "value": "",
                                    "display": False
                                },
                                {
                                    "name": "domestic_wholesale",
                                    "value": "",
                                    "display": False
                                },
                                {
                                    "name": "total_vehicle_&_options",
                                    "value": "",
                                    "display": False
                                },
                                {
                                    "name": "retail_amount",
                                    "value": "",
                                    "display": False
                                },
                                {
                                    "name": "electric_vehicle",
                                    "value": False,
                                },
                                {
                                    "name": "cocar",
                                    "value": False,
                                    "display": False
                                },


                                {
                                    "name": "reynols_report",
                                    "display": False,
                                    "rows": [
                                        {
                                            "field": "Make",
                                            "value": vin_data['flows']["post-inventory"]["report-data"]['make']
                                        },
                                        {
                                            "field": "Stock",
                                            "value": vin_data['flows']["post-inventory"]["report-data"]['stock']
                                        },
                                        {
                                            "field": "VIN",
                                            "value": vin_data['vin']
                                        },
                                        {
                                            "field": "Store",
                                            "value": vin_data['flows']["post-inventory"]["report-data"]['store']
                                        },
                                        {
                                            "field": "Stat Code",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['stat_code']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['stat_code']
                                        },
                                        {
                                            "field": "Received",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['received']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['received']
                                        },
                                        {
                                            "field": "Description",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['desc']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['desc']
                                        },
                                        {
                                            "field": "Inv Amt",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['inv_amt']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['inv_amt']
                                        },
                                        {
                                            "field": "SLS Cost",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['sls_cost']).lower() ==  "nan"  else vin_data['flows']["post-inventory"]["report-data"]['sls_cost']
                                        },
                                        {
                                            "field": "Stock In Notes",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['stock_in_notes']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['stock_in_notes']
                                        },
                                        {
                                            "field": "SVC RO Date",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['svc_ro_date']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['svc_ro_date']
                                        },
                                        {
                                            "field": "JRNL Purch Date DR",
                                            "value": "" if str(vin_data['flows']["post-inventory"]["report-data"]['jrnl_purch_date_dr']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['jrnl_purch_date_dr']
                                        }

                                    ]
                                },

                                {
                                    "name": "stock_in_values",
                                    "display": False,
                                    "rows": [
                                        {
                                            "field": "VIN",
                                            "value": ""
                                        },
                                        {
                                            "field": "STORE",
                                            "value": ""
                                        },
                                        {
                                            "field": "BRAND",
                                            "value": ""
                                        },
                                        {
                                            "field": "PREFIX",
                                            "value": ""
                                        },
                                        {
                                            "field": "REFERENCE",
                                            "value": ""
                                        },
                                        {
                                            "field": "DATE",
                                            "value": ""
                                        },
                                        {
                                            "field": "VENDOR #",
                                            "value": ""
                                        },
                                        {
                                            "field": "INVOICE AMT",
                                            "value": ""
                                        },
                                        {
                                            "field": "INVENTORY AMT",
                                            "value": ""
                                        },
                                        {
                                            "field": "HOLDBACK AMT",
                                            "value": ""
                                        },
                                        {
                                            "field": "PDI ALLOWANCE AMT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 1 AMT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 1 AMT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 1 ACCT1-DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 1 ACCT 2-CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 2 AMT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 2 AMT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 2 ACCT 1-DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 2 ACCT 2-CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 3 AMT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 3 AMT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 3 ACCT 1-DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 3 ACCT 2-CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 4 AMT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 4 AMT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 4 ACCT 1-DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "MFR 4 ACCT 2-CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "VMS ENTRY",
                                            "value": ""
                                        }
                                        
                                    ]
                                },

                                {
                                    "name": "bol_file",
                                    "value": vin_data.get("bol_url", "")
                                },
                                {
                                    "name": "title_file",
                                    "value": vin_data.get("title_url", "")
                                },
                                {
                                    "name": "ro_date",
                                    "value": vin_data.get("ro_date", ""),
                                    #"display": True if vin_data.get("ro_date", "") != "" else False
                                },
                                {
                                    "name": "bol_date",
                                    "value": vin_data.get("bol_date", "")
                                }
                            ],
                       }
                   ]
                }
            }
        }

    def construct_create_request_used_car(self, vin_data, file_content):
        """
        This function constructs the request to send a workitem to the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        app_id = self.app_id
        self.url = f"{aria_url}/public/v1/apps/{app_id}/document_processing"

        pattern = r"^(lexus|porsche|honda|ford|lincoln|cadillac|gm|mazda|jaguar|landrover)"
        text_to_get_make = vin_data['flows']["used-cars"]["report-data"]['make'].lower().replace(" ", "")
        match = re.match(pattern, text_to_get_make)
        make_val = vin_data['flows']["used-cars"]["report-data"]['make'].replace(" ", "")
        if match:
            make_val = match.group()

        # Construct the payload
        group_name = os.environ['GROUP_NAME_USED_CARS']
        self.payload = {
            "data": {
                "type": "CaseManagement",
                "attributes": {
                   "groups": [
                       {
                            "name": group_name,
                            "content": "data:application/pdf;base64," + file_content if file_content != "" else "",
                            "metadata": [

                                {
                                    "name": "vin",
                                    "value": vin_data['vin']
                                },
                                {
                                    "name": "store",
                                    "value": vin_data['flows']["used-cars"]["report-data"]['store'].replace(" ", ""),
                                },
                                {
                                    "name": "make",
                                    "value": make_val.upper(),
                                },
                                {
                                    "name": "stock",
                                    "value": vin_data['flows']["used-cars"]["report-data"]['stock'].replace(" ", ""),
                                },
                                {
                                    "name": "transport_amount",
                                    "value": str(vin_data['flows']["used-cars"]["report-data"]['memo']).replace(" ", ""),
                                },

                                {
                                    "name": "reynols_report",
                                    #"display": False,
                                    "rows": [
                                        {
                                            "field": "Store",
                                            "value": vin_data['flows']["used-cars"]["report-data"]['store']
                                        },
                                        {
                                            "field": "Received",
                                            "value": "" if str(vin_data['flows']["used-cars"]["report-data"]['received']).lower() ==  "nan" else vin_data['flows']["used-cars"]["report-data"]['received']
                                        },
                                        {
                                            "field": "Stock",
                                            "value": vin_data['flows']["used-cars"]["report-data"]['stock']
                                        },
                                        {
                                            "field": "VIN",
                                            "value": vin_data['vin']
                                        },
                                        {
                                            "field": "Make",
                                            "value": vin_data['flows']["used-cars"]["report-data"]['make'].replace(" ", "")
                                        },
                                        {
                                            "field": "Inv Amt",
                                            "value": "" if str(vin_data['flows']["used-cars"]["report-data"]['inv_amt']).lower() ==  "nan" else vin_data['flows']["used-cars"]["report-data"]['inv_amt']
                                        },
                                        {
                                            "field": "SLS Cost",
                                            "value": "" if str(vin_data['flows']["used-cars"]["report-data"]['sls_cost']).lower() ==  "nan"  else vin_data['flows']["used-cars"]["report-data"]['sls_cost']
                                        },
                                        {
                                            "field": "Stock In Notes",
                                            "value": "" if str(vin_data['flows']["used-cars"]["report-data"]['stock_in_notes']).lower() ==  "nan" else vin_data['flows']["used-cars"]["report-data"]['stock_in_notes']
                                        }
                                    ]
                                },

                                {
                                    "name": "stock_in_values",
                                    #"display": False,
                                    "rows": [
                                        {
                                            "field": "VIN",
                                            "value": ""
                                        },
                                        {
                                            "field": "STORE",
                                            "value": ""
                                        },
                                        {
                                            "field": "BRAND",
                                            "value": ""
                                        },
                                        {
                                            "field": "PREFIX",
                                            "value": ""
                                        },
                                        {
                                            "field": "REFERENCE",
                                            "value": ""
                                        },
                                        {
                                            "field": "DATE",
                                            "value": ""
                                        },
                                        {
                                            "field": "APEX AMOUNT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "APEX AMOUNT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "APEX ACCT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "APEX ACCT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "TRANSPORT AMOUNT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "TRANSPORT AMOUNT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "TRANSPORT ACCT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "TRANSPORT ACCT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "DOWC AMOUNT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "DOWC AMOUNT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "DOWC ACCT DT",
                                            "value": ""
                                        },
                                        {
                                            "field": "DOWC ACCT CR",
                                            "value": ""
                                        },
                                        {
                                            "field": "INVENTORY",
                                            "value": ""
                                        },
                                        {
                                            "field": "PAYOFF",
                                            "value": ""
                                        },
                                        {
                                            "field": "PAYABLE",
                                            "value": ""
                                        }
                                    ]
                                },
                            ],
                       }
                   ]
                }
            }
        }

    def construct_create_request_pre_inventory(self, vin_data, file_content):
        """
        This function constructs the request to send a workitem to the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        app_id = self.app_id
        self.url = f"{aria_url}/public/v1/apps/{app_id}/document_processing"

        input_data = [
            {
                "field": "VIN",
                "value": vin_data['vin']
            },
        ]
        for k, v in vin_data['flows']["pre-inventory"]["report-data"].items()[:-1]:
            dict_data = {}
            k_pretty = k.replace("_", " ")
            k_pretty = k_pretty[0].upper() + k_pretty[1:]
            dict_data["field"] = k_pretty
            dict_data["value"] = v
            input_data.append(dict_data)

        # Construct the payload
        group_name = os.environ['GROUP_NAME_PRE_INVENTORY']
        self.payload = {
            "data": {
                "type": "CaseManagement",
                "attributes": {
                   "groups": [
                       {
                            "name": group_name,
                            "content": "data:application/pdf;base64," + file_content if file_content != "" else "",
                            "metadata": [

                                {
                                    "name": "vin",
                                    "value": vin_data['vin']
                                },
                                {
                                    "name": "store",
                                    "value": vin_data['flows']["pre-inventory"]["report-data"]['store'].replace(" ", ""),
                                },
                                {
                                    "name": "make",
                                    "value": vin_data['flows']["pre-inventory"]["report-data"]['make'].replace(" ", ""),
                                },
                                

                                {
                                    "name": "input_data",
                                    #"display": False,
                                    "rows": input_data
                                },

                                {
                                    "name": "stock_in_values",
                                    #"display": False,
                                    "rows": [
                                        {
                                            "field": "VIN",
                                            "value": ""
                                        },
                                        {
                                            "field": "STORE",
                                            "value": ""
                                        },
                                        {
                                            "field": "BRAND",
                                            "value": ""
                                        },
                                        {
                                            "field": "PREFIX",
                                            "value": ""
                                        },
                                        {
                                            "field": "REFERENCE",
                                            "value": ""
                                        },
                                        {
                                            "field": "DATE",
                                            "value": ""
                                        },
                                        {
                                            "field": "ORDER",
                                            "value": ""
                                        },
                                        {
                                            "field": "GENERAL COLOR",
                                            "value": ""
                                        },
                                        {
                                            "field": "EXTERIOR COLOR",
                                            "value": ""
                                        },
                                        {
                                            "field": "INTERIOR COLOR",
                                            "value": ""
                                        },
                                        {
                                            "field": "LIST PRICE",
                                            "value": ""
                                        },
                                        {
                                            "field": "SALES COST",
                                            "value": ""
                                        },
                                        {
                                            "field": "BASE MSRP",
                                            "value": ""
                                        },

                                    ]
                                }
                            ]
                       }
                   ]
                }
            }
        }

    def construct_user_email_request(self, user_id):
        """
        This function constructs the request to retrieve the user's email from the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        self.url = f"{aria_url}/public/v1/users/user_email_by_id?user_id={user_id}"

    def construct_reply_bre_request(self, app_id, item_id, bre_response):
        """
        This function constructs the request to send a BRE response to the ARIA CM.
        """

        # Construct the URL
        aria_url = self.credentials['url']
        app_id = self.app_id
        self.url = f"{aria_url}/public/v1/apps/{app_id}/case_management_middleware/work_items/{item_id}/bre"

        # Construct the payload
        self.payload = {
			"data":{
				"type":"workItem",
				"id": item_id,
				"attributes":{
					"response": bre_response
				}
			}
		}
    
    def send_post_request(self):
        """
        This function sends a POST request to the ARIA CM.
        """

        # Send the request to the CM
        print(self.payload)
        response = requests.post(self.url, headers=self.headers, json=self.payload)
        
        print("****** SEE THE PAYLOAD SENT TO ARIA 😈 ", self.payload)

        # Check status code - it should be 200 and include a link, otherwise raise an exception
        if response.status_code != 200:
            try:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.json()}")
            except requests.exceptions.JSONDecodeError:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.text}")
        
        response_url = response.json().get('links').get('self')
        aria_url = self.credentials['url']
        work_item_data = self.retrieve_connector_response(aria_url + response_url).get("data", {}) \
            .get("attributes", {}).get("document")
        return work_item_data
    
    def send_get_request(self):
        """
        This function sends a GET request to the ARIA CM.
        """

        # Send the request to the CM
        response = requests.get(self.url, headers=self.headers)

        # Check status code - it should be 200 and include a link, otherwise raise an exception
        if response.status_code not in [200, 202]:
            try:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.json()}")
            except requests.exceptions.JSONDecodeError:
                raise Exception(f"Failed to send request to ARIA CM: {response.status_code} - {response.text}")
        
        if response.status_code == 200:
            return response.json()
        else:
            time.sleep(2)
            return self.retrieve_connector_response(self.url, try_count=1)

    def retrieve_connector_response(self, response_url, try_count=0):
        """
        This function retrieves the response from the connector. It will retry up to 2 times
        if the response is not ready.
        """
        if try_count > 10:
            raise TemporalUnavailableException("Failed to retrieve the connector response after 10 attempts")
        
        print("RESPONSE URL ", response_url)
        
        response = requests.get(response_url, headers=self.headers)
        if response.status_code == 202:
            try_count += 1
            time.sleep(2*try_count)
            return self.retrieve_connector_response(response_url, try_count)

        elif response.status_code == 200:
            return response.json()

        else:
            try:
                raise Exception(f"Failed to retrieve connector response: {response.status_code} - {response.json()}")
            except requests.exceptions.JSONDecodeError:
                raise Exception(f"Failed to retrieve connector response: {response.status_code} - {response.text}")

