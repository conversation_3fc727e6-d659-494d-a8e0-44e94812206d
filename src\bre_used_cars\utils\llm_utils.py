from openai import AzureOpenAI
from openai import OpenAI
import boto3
from botocore.exceptions import ClientError


class LLM():
    def __init__(self, engine, llm_params):
        if 'gpt' in engine:
            if llm_params['deployment'] == 'azure':
                self.client = AzureOpenAI(
                    azure_endpoint = llm_params['endpoint'], 
                    api_key = llm_params['api_key'],  
                    api_version = llm_params['api_version']
                )
            elif llm_params['deployment'] == 'public':
                self.client = OpenAI(api_key=llm_params['api_key'])
            else:
                return 'Deployment not supported'
        elif llm_params['deployment'] == 'bedrock':
            self.client = boto3.client("bedrock-runtime", region_name=llm_params['region_name'])
        
        self.model = llm_params['model']
        self.engine = llm_params['engine']
        self.deployment = llm_params['deployment']
        self.prompt = ''

    def generate_prompt(self, prompt):
        self.prompt = prompt

    def send_message_to_llm(self, message):
        if 'gpt' in self.engine:
            print('------------Message to GPT-----------')
            print(message)
            print('-----------------------')
            response = self.client.chat.completions.create(
                model = self.model,
                messages=[
                    {"role": "system", "content": self.prompt},
                    {"role": "user", "content": message}
                ],
                timeout = 120
            )
            print('------------GPT output-----------')
            print(response)
            print('-----------------------')
            return response.choices[0].message.content
        elif self.deployment == 'bedrock':
            print(f'------------Message to {self.engine}-----------')
            print(message)
            print('-----------------------')
            response = self.client.converse(
                modelId=self.model,
                messages= [
                    {"role": "user", "content": [{"text": message}]}
                ],
                system=[
                    {'text': self.prompt},
                ],
                inferenceConfig={"maxTokens": 2000, "temperature": 0.7, "topP": 1},
            )
            print(f'------------{self.engine} output-----------')
            print(response)
            print('-----------------------')
            return response["output"]["message"]["content"][0]["text"]
        else:
            return 'Engine not supported'
    
    @staticmethod
    def get_consistent_output(llm_params, prompt, message, expected, attempts=3):
        if len(expected) != 2:
            raise ValueError("Expected list must contain exactly two elements.")
        
        outputs = []

        for _ in range(attempts):
            gpt_output = LLM.call_llm(llm_params, prompt, message)
            outputs.append(str(gpt_output).lower())

        return expected[0] if outputs.count(expected[0]) >= attempts / 2 else expected[1]
    
    @staticmethod
    def call_llm(self, llm_params, prompt, message):
        llm = LLM(llm_params['engine'], llm_params=llm_params)
        llm.generate_prompt(prompt=prompt)
        llm_output = llm.send_message_to_llm(message)
        print(llm_output)
        return llm_output
