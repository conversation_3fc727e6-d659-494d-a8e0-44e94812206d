fields = {
    "porsche_invoices_pre_inventory":{
        "VIN": "<17-character Vehicle Identification Number (VIN). Bear in mind that VIN numbers don’t have 'O', 'I', and 'Q' letters to avoid confusing them with '0', '1', and '9'. Shown as 'VIN' in the document.>",
        "Model code": "<Alphanumeric car model code. Shown as 'MTYP' in the document.>",
        "Model description": "<Car model. It can be found in the first line of the prices table.>",
        "Invoice Date": "<The date when the invoice was issued. Should be at the start of the document. I need it in american format that is MM/DD/YYYY (Ex: January 14, 2025 --> 01/14/2025))>",
        "Sold to": "<Dealer shop address. Shown below 'SOLD TO' in the document. Extract only the address (Street Address, City, State and ZIP code)>",
        "Retail Total Price": "<Total retail price amount. Shown as 'Total Retail Price' in the document.>",
        "Order code": "<Is the order code. Shown under the tag 'Comm. No.' at the start of the document.>",
        "Color": "<Is the color code that the car has. Shown as an alphanumeric code under the tag 'Color' at the start of the document.>",
        "Table":"<The line items that can be found in the Invoice. Will be duplicated so avoid the duplication. For each line it must be extracted the following: MTYP/Option (typically a 3 digits alphanumeric code), Description (undet 'Text' label) and Retail Price that is the amount. All the values are always present."
    }
}

fields_json = {
    "porsche_invoices_pre_inventory":{
        "VIN": "WP0AF2A95SS178842 [Id: 'unique_id_1']",
        "Model code": "992458 [Id: 'unique_id_2']",
        "Model description": "911 [Id: 'unique_id_3'] GT3 [Id: 'unique_id_4'] RS [Id: 'unique_id_5']",
        "Invoice Date": "01/23/2025 [Id: 'unique_id_6']",
        "Sold to": "1234 [Id: 'unique_id_9'] Fake [Id: 'unique_id_7'] Avenue [Id: 'unique_id_8'] Atlanta [Id: 'unique_id_9'] GA [Id: 'unique_id_10'] 30342 [Id: 'unique_id_11']",
        "Retail Total Price": "295,198.00 [Id: 'unique_id_12']",
        "Table": [
            {
                "Code":"7224 [Id: 'unique_id_14']",
                "Description":"Graphite [Id: 'unique_id_15'] Gray [Id: 'unique_id_16']",
                "Price":"$712 [Id: 'unique_id_17']",
            },
            {
                "Code":"C01PKG [Id: 'unique_id_18']",
                "Description":"Driver [Id: 'unique_id_19'] Convenience [Id: 'unique_id_20'] Package [Id: 'unique_id_21']",
                "Price":"$424 [Id: 'unique_id_22']",
            },
            {
                "Code":"805 [Id: 'unique_id_23']",
                "Description":"Technical [Id: 'unique_id_24'] Documentation [Id: 'unique_id_25']",
                "Price":"$1234 [Id: 'unique_id_26']",
            }
        ],
        "Order code": "A12743 [Id: 'unique_id_27']",
        "Color": "4G2K2H [Id: 'unique_id_28']"
    }
}

fields_json_v2 = {
    "porsche_invoices_pre_inventory":{
        "VIN": "WP0AF2A95SS178842",
        "Model code": "992458",
        "Model description": "911",
        "Invoice Date": "01/23/2025",
        "Sold to": "1234",
        "Retail Total Price": "295,198.00",
        "Table": [
            {
                "Code":"7224",
                "Description":"Graphite Gray",
                "Price":"$712",
            },
            {
                "Code":"C01PKG",
                "Description":"Driver Convenience Package",
                "Price":"$424",
            },
            {
                "Code":"805",
                "Description":"Technical Documentation",
                "Price":"NONE",
            }
        ],
        "Order code": "A12743",
        "Color": "4G2K2H"

    }
}

fields_with_threshold = {
    "porsche_invoices_pre_inventory":{}
}

fields_type = {
    "porsche_invoices_pre_inventory":{
        "VIN": {"type": "regular"},
        "Model code": {"type": "regular"},
        "Model description": {"type": "regular"},
        "Invoice Date": {"type": "regular"},
        "Sold to": {"type": "regular"},
        "Retail Total Price": {"type": "regular"},
        "Table": {"type": "table"},
        "Code": {"type": "regular"},
        "Description": {"type": "regular"},
        "Price": {"type": "regular"},
        "Order code": {"type": "regular"},
        "Color": {"type": "regular"},
    }
}