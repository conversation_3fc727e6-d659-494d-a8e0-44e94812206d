import os
import json
from botocore.exceptions    import ClientError

from crud_statuses          import CrudStatuses
from crud_handler_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>
from boto3_utils            import trigger_lambda, trigger_lambda_response, get_secret
from mongo_utils import Mongo
from actions import actions_dict
from aria_utils import ARIA

class BreHandler():
    def __init__(self, event):

        self.mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(self.mongo_uri)
    
        self.event = event
        self.input_body = json.loads(event['body'])
        self.action_data = self.input_body['action']
        self.document = self.input_body['document']
        self.app_id = self.document['app_id']
        self.statuses = self.input_body['status']

        self.crud_handler = CrudHandler(self.mongo)
        self.crud_statuses = CrudStatuses(self.mongo)

        print(os.environ.get('LLM_LAMBDA'))

        self.llm_by_app_id = {
            f"{os.environ.get('ARIA_APP_ID_POST_INVENTORY')}": os.environ.get('LLM_LAMBDA'),
            f"{os.environ.get('ARIA_APP_ID_BOLS')}": os.environ.get('LLM_LAMBDA'),
            f"{os.environ.get('ARIA_APP_ID_TITLES')}": os.environ.get('LLM_LAMBDA'),
            f"{os.environ.get('ARIA_APP_ID_PRE_INVENTORY')}": os.environ.get('LLM_LAMBDA_PRE_INVENTORY'),
            f"{os.environ.get('ARIA_APP_ID_USED_CARS')}": os.environ.get('LLM_LAMBDA_USED_CARS'),
        }

    def next_step(self):
        """
        Decides what lambda calls, if its the first time that Aria process the work item
        it will call to the LLM, if not the it will call another lambda specified for each action
        """
        next_function = None
        bre_type = ""
        action_name = self.action_data.get('action_label', None)
        if action_name:
            action_name = action_name.lower()


        print("ACTION TO DO", action_name)
        self.crud_statuses.insert_or_update(self.app_id, self.statuses)
        

        #if action_name not in list(actions_dict[document_type].keys()) or action_name is None:
        if action_name is None:
            # If not action provided its cause its a new work item so we must call
            # the llm lambda

            # Check if its a document without group (no pdf)
            if len(self.document.get('ocr_groups')) == 0:
                for k,v in self.statuses.items():
                    if 'needs' in v['label'].lower():
                        target_status_label = v['label']
                        target_status_id = k
                        break
                aria_exception = "No OCR. Cant execute bre rules"
                secret = get_secret(secret_name=f'{os.environ["ENV"]}-aria_cm_tokens')
                aria = ARIA(base_url=secret[os.environ.get('ARIA_ENV')]['url'], request_token=secret[os.environ.get('ARIA_ENV')]['token'])
                aria.bre_reply(
                    app_id=self.document['app_id'],
                    item_id=self.document['id'],
                    bre_response={
                        "aria_status":{"value": target_status_id},
                        "aria_exception":{"value": aria_exception}
                    })

                bre_type = "document_without_group"
            
            next_function = self.llm_by_app_id[self.app_id]
            method = trigger_lambda
            request_response = False

            # Store the statuses in mongo
            self.crud_statuses.insert_or_update(self.app_id, self.statuses)

        else:

            if self.app_id == os.environ.get('ARIA_APP_ID_USED_CARS') and len(self.document.get('ocr_groups')) == 0:
                document_type = "used_cars_invoices"
            else:
                document_type = self.document['ocr_groups'][0]

            if action_name in actions_dict[document_type]:
                action_vals = actions_dict[document_type][action_name]
                next_function = os.environ.get(action_vals.get('next_function'))
                request_response = action_vals.get('request_response')

                method = trigger_lambda_response
                if not request_response:
                    method = trigger_lambda

                if action_vals.get('request_response', '') != '':
                    bre_type = action_vals.get('bre_type') 

                if action_vals.get('special_conditions', []) != []: 
                    if any(condition in self.document.get('aria_exception', '') for condition in action_vals.get('special_conditions')):
                        bre_type = action_vals.get['if_true_special_conditions']['bre_type']
        
        print("NEXT STEP", next_function, method, request_response)

        return bre_type, next_function, method, request_response
    
    def insert_execution_mongo(self, next_function):
        # Insert execution in mongo
        try:
            execution_id = self.crud_handler.insert_execution(self.input_body, next_function)
            print(next_function)
            print(execution_id)
        except Exception as e:
            return {
                'statusCode': 500,
                'body': json.dumps({'message': 'Issue while inserting execution in mongo: ' + str(e)})
            }
        
        return execution_id
    
    def run(self):
        try:

            bre_type, next_function, method, request_response = self.next_step()

            if bre_type == "document_without_group":
                return {
                    'statusCode': 200,
                    'body': json.dumps({'message': 'No OCR to process'})
                }
        
            # Save on db what we selected on this run
            execution_id = self.insert_execution_mongo(next_function)

            # Update the information on the input_body
            self.input_body['bre_type'] = bre_type
            self.input_body['execution_id'] = execution_id
            self.input_body['request_response'] = request_response

            # Calling the next step
            try:
                lambda_response = method(next_function, self.input_body)
            except Exception as e:
                self.crud_handler.mark_as_failed(execution_id, error_message=str(e))
                return {
                    'statusCode': 500,
                    'body': json.dumps({'message': 'Issue while triggering next lambda: ' + str(e)})
                }

            # If the lambda is not the llm lambda we wait for its response
            if method == trigger_lambda_response:
                response_payload = json.loads(lambda_response['Payload'].read())
                print("RESPONSE FROM LAMBDA ->> ", response_payload)
                
                return {
                    'statusCode': response_payload.get('statusCode'),
                    'body': response_payload.get('body')
                }
            
            return {
                'statusCode': 200,
                'body': json.dumps({'message': 'Working on it'})
            }
        
        except Exception as e:
            self.crud_handler.insert_execution(self.event, 'None', failed=True, error_message=str(e))
            return {
                'statusCode': 500,
                'body': {'message': 'Issue while processing the petition: ' + str(e)}
            }
    

def lambda_handler(event, context):
    print(event)
    if 'body' not in list(event.keys()):    
        raise ValueError("body tag is missing on the dict. Skipping...")
    
    bre_handler = BreHandler(event)
    return bre_handler.run()