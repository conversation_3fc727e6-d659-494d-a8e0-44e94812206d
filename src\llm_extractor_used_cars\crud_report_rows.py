import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudReynolsReport:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )

    def update_row_by_vin(self, vin, data_to_set, data_to_push):
        query = {"vin": vin}
        data_to_set['updated_at'] = datetime.datetime.now()
        data = {"$set": data_to_set, "$push": data_to_push} 
        self.mongo.update_one(query, data)

    def update_row_by_aria_wi_id(self, aria_wi_id, data_to_set, data_to_push):
        query = {"flows.used-cars.docs.invoice.aria_data.aria_wi_id": aria_wi_id}
        data_to_set['updated_at'] = datetime.datetime.now()
        data = {"$set": data_to_set, "$push": data_to_push} 
        self.mongo.update_one(query, data)

    def find_report_row_by_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        return self.mongo.find_one(query)


    def __del__(self):
        self.mongo.close_connection()
