import os
import json
import traceback
from bre_files.used_cars_invoices_engine.invoice_bre_engine import UsedCarsInvoicesBreEngine
from utils.boto3_utils            import get_secret

def lambda_handler(event, context):

    # Retrieve input data
    print(event)
    
    if event is None or event is {}:
        raise ValueError("Event empty. Skipping...")
    
    mongo_uri = get_secret(f'{os.environ["ENV"]}-mongodb_uri', False)


    # document = event.get('document', {})
    # ocr_groups = document.get('ocr_groups', [])
    # if not ocr_groups:
    #     raise Exception('No groups found in document')
    
    """
    This BRE is different as in USED CARS process we are processing vins with invoice and without invoice
    that's why in some places we add the ocr group to let the BRE execute the rules.
    """
    bre = UsedCarsInvoicesBreEngine(event, mongo_uri)

    bre_response = bre.run()

    print("********** BRE RESPONSE **********")
    print(bre_response)
    print("*********************************")
    
    return bre_response


        

        



    