from utils.mongo_utils            import Mongo
from utils.crud_handler_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.crud_bre_results import CrudBreResults
from datetime import datetime
import os
import json
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
import re
from typing import Dict, Any, Tuple
from utils.aria_utils import ARIA
from utils.boto3_utils import get_secret
from utils.crud_vins import CrudVins
from utils.crud_invoices import CrudInvoices
import pymongo
class Bre():

    def __init__(self, event, mongo_uri):

        # Init mongo
        self.mongo_client = Mongo(mongo_uri)
        self.mongo_uri = mongo_uri

        self.rules = []
        self.valid_rules = []
        self.result = {}
        self.results_json = {}
        self.rules_with_pre_req = []
        self.bre_rules = {}
        self.passed_rules = []
        self.not_passed_rules = []
        
        self.now_in_ms = datetime.now()

        self.event = event
        self.bre_type = self.event.get('bre_type', '')
        self.document = self.event.get('document', {})
        self.action = self.event.get('action', {})
        self.status_info = event.get('status', {})
        self.execution_id = self.event.get('execution_id', '')
        self.request_response = self.event.get('request_response', False)
        self.rules_output_log = {}
        self.crud_handler = CrudHandler(self.mongo_client)
        self.crud_bre_results = CrudBreResults(self.mongo_client)
        self.crud_reynols_report = CrudVins(self.mongo_client)
        self.crud_invoices = CrudInvoices(self.mongo_client)


        mongo_uri = get_secret(f"{os.environ['ENV']}-mongodb_uri", return_json = False)
        client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        self.db = client[os.environ['MONGO_DATABASE']]

        secret = get_secret(secret_name=f'{os.environ["ENV"]}-aria_cm_tokens')
        self.aria = ARIA(base_url=secret[os.environ['ARIA_ENV']]['url'], request_token=secret[os.environ['ARIA_ENV']]['token'])

        # Parsing input json
        self.document_id = self.document['id']
        self.app_id = self.document.get('app_id')
        
        if not self.app_id:
            raise Exception("app_id is missing from the document")
                    
        self.ocr_groups = self.document.get('ocr_groups', [])
        if not self.ocr_groups:
            raise Exception('No groups found in document')
        
        
        group = self.ocr_groups[0]
        self.document_type = group

        if self.bre_type == '':
            self.bre_type = {'invoices_pre_inventory': os.environ['DEFAULT_BRE_TYPE']}

        print("RULES", self.valid_rules)
        print(self.bre_type)
            
        self.extracted_fields = {}
        for group in self.ocr_groups:
            group_fields = self.document['groups'].get(group, {}).get('fields', {})
            self.extracted_fields[group] = group_fields

        self.parsed_fields = {}
        self.parsed_fields = self.get_parsed_fields()
        
    def clean_and_convert(value):
        # Remove all non-numeric characters except `.`
        cleaned_value = re.sub(r"[^\d.]", "", str(value))

        # Ensure there's only one decimal point by keeping the first occurrence
        if cleaned_value.count('.') > 1:
            parts = cleaned_value.split('.')
            cleaned_value = parts[0] + '.' + ''.join(parts[1:])  # Keep first dot, remove others

        return float(cleaned_value) if cleaned_value else ""  # Convert to float safely
    
    def non_empty_rule(self, input_data, field):
        field_pretty = field.replace(' ', '_').lower()
        
        # Checking if input_data is not a str. If so, force it to ""
        input_data = '' if not isinstance(input_data, str) else input_data
        self.result[field_pretty] = {'pass': True, 'value': input_data, 'display': True}
        
        if input_data.replace(' ', '') == '':
            self.result[field_pretty]['pass'] = False
            self.result[field_pretty]['message'] = f'{field} not found'
            self.result[field_pretty]['value'] = input_data
        else:
            self.result[field_pretty]['value'] = input_data.rstrip()

    
            
    def clean_amount_value(self, input_data):
        """This method removes everything that is not a number"""
        # Checking if input_data is not a str. If so, force it to ""
        input_data = '' if not isinstance(input_data, str) else input_data    
        clean_value = re.sub(r'[^\d.]', '', input_data)
        return clean_value

    def is_valid_date(self, date_str):
        pattern = r"^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/\d{4}$"
        return bool(re.match(pattern, date_str))


    def call_rule(self, rule_nr, rule_json, passed_rules):
        process = True
        pre_req = rule_json['pre_req']

        for item in pre_req:
            if item > 0:
                if item not in passed_rules:
                    process = False
            else:
                if abs(item) in passed_rules:
                    process = False
        if process:
            rule_method = getattr(self, f"rule_{rule_nr}")
            rule_method()

        return process
        
    def execute_rules(self):

        
        print("PARSED_FIELDS PRE EXECUTION", self.parsed_fields)
        self.rules_with_pre_req = [key for key, val in self.bre_rules.items() if len(val['pre_req']) > 0 and key in self.valid_rules]
        self.rules_without_pre_req = list(set(self.valid_rules) - set(self.rules_with_pre_req))

        def execute_rule(item):
            """Helper function to execute a rule and return results."""
            self.call_rule(item, self.bre_rules[item], self.passed_rules)
            return item

        # **1. Execute rules WITHOUT prerequisites in parallel**
        with ThreadPoolExecutor(max_workers=8) as executor:
            future_to_rule = {executor.submit(execute_rule, item): item for item in self.rules_without_pre_req}
            
            for future in as_completed(future_to_rule):
                item = future.result()
                if self.result:
                    if self.result[self.bre_rules[item]['tag']]['pass']:
                        self.passed_rules.append(item)
                        for tag in self.bre_rules[item]['output_tags']:
                            self.results_json[tag] = self.result[tag]
                    else:
                        self.not_passed_rules.append(item)
                        for tag in self.bre_rules[item]['output_tags']:
                            if self.result.get(tag, None):
                                self.results_json[tag] = self.result[tag]
                    
                    if self.result.get('aria_exception', None):
                        self.result.pop('aria_exception')

        # **2. Execute rules WITH prerequisites sequentially**
        index = 0
        iterations = 0
        max_iterations = len(self.rules_with_pre_req) * (len(self.rules_with_pre_req) + 1) // 2
        
        if len(self.rules_with_pre_req) > 0:
            while len(self.rules_with_pre_req) > 0 and iterations < max_iterations:
                item = self.rules_with_pre_req[index]
                processed = self.call_rule(item, self.bre_rules[item], self.passed_rules)

                if processed:
                    self.rules_with_pre_req.remove(item)  # index is valid here

                    if self.result:
                        if self.result.get(self.bre_rules[item]['tag'], {}).get('pass'):
                            self.passed_rules.append(item)
                            for tag in self.bre_rules[item]['output_tags']:
                                self.results_json[tag] = self.result.get(tag)
                        else:
                            self.not_passed_rules.append(item)
                            for tag in self.bre_rules[item]['output_tags']:
                                if self.result.get(tag) is not None:
                                    self.results_json[tag] = self.result[tag]

                        self.result.pop('aria_exception', None)

                else:
                    index += 1

                # Wrap index if needed
                if len(self.rules_with_pre_req) > 0 :
                    index %= len(self.rules_with_pre_req)

                iterations += 1
            

        return self.results_json, self.passed_rules, self.not_passed_rules
    
    def get_parsed_fields(self):
        parsed_fields = {}
        # Process fields from each group
        for group_name, group_fields in self.extracted_fields.items():
            parsed_fields[group_name] = {}
            for k, v in group_fields.items():
                if isinstance(v.get('value'), str):
                    parsed_fields[group_name][k] = {
                        'display': v.get('display', True),
                        'pass': True,
                        'message': '',
                        'value': v.get('value', v.get('rows', '')),
                        'coordinates': v.get('coordinates', {})
                    }
                else:

                    parsed_fields[group_name][k] = v
                    if not parsed_fields[group_name][k].get('pass', False):
                        parsed_fields[group_name][k]['pass'] = True
                        parsed_fields[group_name][k]['message'] = ''

                    if "rows" in v:
                        for kk, vv in parsed_fields[group_name][k].get('rows', {}).items():
                            for kkk, vvv in vv['cells'].items():
                                vvv['pass'] = True
                                vvv['message'] = ''

                    else:
                        parsed_fields[group_name][k] = v
                        if not parsed_fields[group_name][k].get('pass', False):
                            parsed_fields[group_name][k]['pass'] = True
                            parsed_fields[group_name][k]['message'] = ''
        return parsed_fields
    
    def save_bre_results(self):
        bre_results = self.crud_bre_results.find_bre_results_by_wi_id(self.document_id)
        if bre_results is None:
            iteration = 1
            self.mongo_client.insert_one({"aria_wi_id": self.document_id, "total_iterations": 1, "iterations": {"1": {"time": self.now_in_ms.strftime("%m/%d/%Y-%H:%M:%S.%f"), "input": self.event, "output": "", "passed_rules": "", "not_passed_rules": "", "success": "", "error_message": ""}}})
        else:
            iteration = bre_results['total_iterations'] + 1
            self.mongo_client.update_one(filter={"aria_wi_id": self.document_id}, data={"$set": {"total_iterations": iteration, f"iterations.{iteration}": {"time": self.now_in_ms.strftime("%m/%d/%Y-%H:%M:%S.%f"), "input": self.event, "output": "", "passed_rules": "", "not_passed_rules": "", "success": "", "error_message": ""}}})

    def save_post_iteration_bre_results(self, target_status_label):
        bre_results = self.crud_bre_results.find_bre_results_by_wi_id(self.document_id)
        if bre_results:
            iteration = bre_results['total_iterations']
            self.crud_bre_results.update_bre_results_by_wi_id(
                filter={"aria_wi_id": self.document_id},
                data={"$set": {
                f"iterations.{iteration}.time": datetime.now(),
                f"iterations.{iteration}.input": self.event,
                f"iterations.{iteration}.output": self.parsed_fields,
                f"iterations.{iteration}.passed_rules": self.passed_rules,
                f"iterations.{iteration}.not_passed_rules": self.not_passed_rules,
                f"iterations.{iteration}.success": True,
                f"iterations.{iteration}.error_message": '',
                f"iterations.{iteration}.target_status": target_status_label
            }})

    def update_parsed_fields_with_execution_rules_output(self, execution_rules_output, group_name):
        # Ensure the group exists in parsed_fields
        if group_name not in self.parsed_fields:
            self.parsed_fields[group_name] = {}
            
        # Updating output json with error messages and values for the specific group
        for k,v in execution_rules_output.items():
            if self.parsed_fields[group_name].get(k, None) == None:
                self.parsed_fields[group_name][k] = {}
            self.parsed_fields[group_name][k]['display'] = v.get('display', True)
            self.parsed_fields[group_name][k]['pass'] = v.get('pass', True)
            self.parsed_fields[group_name][k]['message'] = v.get('message', '')
            self.parsed_fields[group_name][k]['value'] = v.get('value', '')
            #if v.get('coordinates', None):
            if 'coordinates' in v:
                self.parsed_fields[group_name][k]['coordinates'] = v.get('coordinates')

    def get_status_label_info(self, status: str, status_info: Dict[str, Any]) -> Tuple[str, str]:
        for k, v in status_info.items():
            if status in v['label'].lower():
                return k, v['label']
        return '', ''

    def get_status_label_by_status_id(self, status_id: str, status_info: Dict[str, Any]) -> Tuple[str, str]:
        for k, v in status_info.items():
            if k == status_id:
                return v['label']
        return ''

    def update_invoice(self, next_status_label):
        if "invoice" in self.ocr_groups[0]:
                
            rows_report = self.parsed_fields[self.ocr_groups[0]]['input_data']['rows'] or self.parsed_fields[self.ocr_groups[0]]['fields']['input_data']['value']
            vin = ''
            for k, v in rows_report.items():
                if v['cells']['field']['value'] == 'VIN':
                    vin = v['cells']['value']['value']
                    break

            if vin != '':
                self.crud_reynols_report.update_row_by_vin(
                    vin=vin, 
                    data_to_set={
                        "flows.pre-inventory.docs.invoice.fields": self.parsed_fields[self.ocr_groups[0]],
                        "flows.pre-inventory.docs.invoice.aria_data.status": next_status_label,
                        "flows.pre-inventory.docs.invoice.updated_at": datetime.now(),
                        "updated_at": datetime.now()
                    },
                    data_to_push={"flows.pre-inventory.docs.invoice.aria_data.status_history": next_status_label})
                
                vin_status = 6
                if "ready" in next_status_label.lower():
                    vin_status = 7
                
                self.crud_reynols_report.update_row_by_vin(vin=vin, data_to_set={"status": vin_status}, data_to_push={"status_history": vin_status})

            self.crud_invoices.update_invoice_by_wi_id(self.document_id, data_to_set={"fields": self.parsed_fields[self.ocr_groups[0]], "status": next_status_label, "updated_at": datetime.now()}, data_to_push={"status_history": next_status_label})
        pass

    def process_default_bre(self):
        pass

    def process_based_on_rules_bre(self):
        pass

    def run(self):

        try:

            self.save_bre_results()

            # Process each group separately
            all_passed_rules = {}
            all_not_passed_rules = {}
            
            # Execute rules for this group
            execution_rules_output, passed_rules, not_passed_rules = self.execute_rules()

            # print("EXECUTION RULES LOGS")
            # print(self.rules_output_log)
            # print("***********************")

            print("EXECUTION RULES OUTPUT")
            print(execution_rules_output)
            print("***********************")

            # Update parsed fields for this group
            self.update_parsed_fields_with_execution_rules_output(execution_rules_output, self.document_type)

            # Store results for this group
            all_passed_rules[self.document_type] = passed_rules
            all_not_passed_rules[self.document_type] = not_passed_rules

            print("********** VALID RULES ", self.valid_rules)
            print("********** PASSED RULES ", self.passed_rules)
            print("********** NOT PASSED RULES ", self.not_passed_rules)


            if self.bre_type[self.ocr_groups[0]] == os.environ['DEFAULT_BRE_TYPE']:
                next_status_id, next_status_label, aria_exception, note = self.process_default_bre()
            else:
                next_status_id, next_status_label, aria_exception, note = self.process_based_on_rules_bre()

            self.update_invoice(next_status_label)

            print("********** PARSED FIELDS ", self.parsed_fields)

            self.save_post_iteration_bre_results(next_status_label)

            print("REQUEST RESPONSE", self.request_response)


            print("********* TO STATE ", next_status_label)

            if not self.request_response:
        
                self.aria.bre_reply(
                    app_id=self.app_id,
                    item_id=self.document_id,
                    bre_response={
                        "aria_status":{"value": next_status_id},
                        "aria_exception":{"value": aria_exception},
                        self.ocr_groups[0]: self.parsed_fields[self.ocr_groups[0]]
                    })
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                        "aria_status":{"value": next_status_id},
                        "aria_exception":{"value": aria_exception},
                        self.ocr_groups[0]: self.parsed_fields[self.ocr_groups[0]]
                })
            }
                    
        except Exception as e:
            print('Error: {}'.format(traceback.format_exc()))
            try:
                self.crud_handler.mark_as_failed(execution_id=self.execution_id)
            except:
                pass
                print('Error updating handler with id {}'.format(self.execution_id))
            return {
                'statusCode': 500,
                'body': json.dumps('Error:'.format(e))
            }