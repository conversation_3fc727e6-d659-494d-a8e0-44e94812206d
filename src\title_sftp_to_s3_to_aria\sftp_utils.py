import os
import paramiko
from stat import S_ISREG
from boto3_utils import get_secret, post_to_s3, get_latest_pdf_metadata
from datetime import datetime
from crud_titles import CrudTitles

def connect_sftp(hostname, username, password, port):
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())  # Automatically add host key
        client.connect(hostname, port=port, username=username, password=password)
        sftp = client.open_sftp()  # Open SFTP session
        return sftp, client
    except paramiko.AuthenticationException:
        print("Auth failed.")
        raise
    except paramiko.SSHException as e:
        print(f"SSH error: {e}")
        raise
    except Exception as e:
        print(f"Error: {e}")
        raise

def download_files_from_sftp(sftp, client, remote_folder, local_folder, bucket_name, folder_name):
    file_extension = os.environ['FILE_SFTP_EXTENSION']

    crud_titles = CrudTitles()

    try:
        sftp.chdir(remote_folder)

        print(sftp.listdir())

        files = [
            (f, sftp.stat(f).st_mtime)
            for f in sftp.listdir()
            if f.endswith(file_extension)
        ]
        today = datetime.today().date()

        today_files = [
            (f, timestamp)
            for f, timestamp in files
            if datetime.fromtimestamp(timestamp).date() == today
        ]

        print(today)
        print(today_files)

        if today_files:
            files = []
            for file, timestamp in today_files:   # Get latest file of today
            
                # Convert timestamp to readable datetime format
                upload_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

                print(f"Latest file from today: {file}, Uploaded at: {upload_time}")

                file_name_with_timestamp = download_and_upload_file(sftp, crud_titles, file, upload_time, local_folder, bucket_name, folder_name)

                if file_name_with_timestamp is not None:
                    files.append((file_name_with_timestamp, upload_time))
            return files
        else:
            print(f"No files from today ({today}) found in the remote folder.")
            return None
        
    except Exception as e:
        print(f"Download error: {str(e)}")
    finally:
        if sftp:
            sftp.close()
        if client:
            client.close()

def download_specific_file_from_sftp(sftp, client, remote_folder, local_folder, bucket_name, folder_name, specific_file):
    try:
        sftp.chdir(remote_folder)
        files = sftp.listdir()

        if specific_file in files:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")  # Format: YYYYMMDD_HHMMSS
            file_name_timestamp = f"{timestamp}_{specific_file}"

            local_path = os.path.join(local_folder, file_name_timestamp)
            os.makedirs(local_folder, exist_ok=True)

            print(f"Downloading file: {specific_file}")
            sftp.get(specific_file, local_path)

            post_to_s3(bucket_name, folder_name, file_name_timestamp, local_path)
            return [(file_name_timestamp, timestamp)]
        else:
            print(f"Specified file '{specific_file}' not found.")
            return None
    except Exception as e:
        print(f"Download error: {str(e)}")
    finally:
        if sftp:
            sftp.close()
        if client:
            client.close()

def download_and_upload_file(sftp, crud_titles, file_name, upload_time, local_folder, bucket_name, folder_name):
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")  # Format: YYYYMMDD_HHMMSS
    file_name_timestamp = f"{timestamp}_{file_name}"

    local_path = os.path.join(local_folder, file_name_timestamp)
    os.makedirs(local_folder, exist_ok=True)

    print(f"Downloading file: {file_name}")
    sftp.get(file_name, local_path)

    data = crud_titles.find_title_by_name_and_uploaded_time(file_name, upload_time)
    if data is not None:
        return

    post_to_s3(bucket_name, folder_name, file_name_timestamp, local_path)
    return file_name_timestamp

def get_titles_file(bucket_name, folder_name, specific_file=None):
    secret = get_secret(os.environ['SFTP_CREDENTIALS'])
    hostname = secret["hostname"]
    username = secret["username"]
    password = secret["password"]
    port = secret["port"]

    remote_folder_path = os.environ['SFTP_FILES_PATH']
    local_download_path = '/tmp/'
    sftp, client = connect_sftp(hostname, username, password, port)

    if specific_file:
        return download_specific_file_from_sftp(sftp, client, remote_folder_path, local_download_path, bucket_name, folder_name, specific_file)
    else:
        return download_files_from_sftp(sftp, client, remote_folder_path, local_download_path, bucket_name, folder_name)
