import pandas as pd
from boto3_utils import get_secret
import pymongo
import os
from datetime import datetime, timedelta
from outlook_utils import Outlook
import traceback
import io
import json

def lambda_handler(event, context):

    print(event)

    stage = event.get("stage", None)
    collection_name = event.get("collection")
    if (not collection_name or not stage) and "invoice" in collection_name.lower():
        print("Error: No collection name provided")
        return {'statusCode': 400, 'body': json.dumps({'message': "Error: No collection or stage name provided"})}
    
    print(f"******+ EXECUTING REPORT FOR: {collection_name.upper()} ******+")

    try:

        # Connect to MongoDB
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        client = pymongo.MongoClient(mongo_uri)
        db = client[os.environ['MONGO_DATABASE']]
        collection = db[collection_name]

        # Get data from the last 1 hour
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        query = {"read_at": {"$gte": one_hour_ago}}
        if "invoice" in collection_name.lower():
            query["current_flow"] = stage
        cursor = collection.find(query)

        # Convert to DataFrame
        df = pd.DataFrame(list(cursor))

        print("EXTRACTED DF")
        print(df)

        if df.empty:
            return {"message": f"No data found for {collection_name} the last hour"}

        # Drop the MongoDB '_id' field if exists
        df.drop(columns=["_id"], errors="ignore", inplace=True)

        # Format timestamps
        if "read_at" in df.columns:
            df["read_at"] = df["read_at"].dt.strftime("%Y-%m-%d %H:%M:%S")
        
        now_date = datetime.now()
        file_date_format = str(now_date.year) + str(now_date.month) + str(now_date.day) + "_" + str(now_date.hour)
        summary_file_name = f"{file_date_format}_LOADED_DATA_{collection_name.upper()}_report.csv"

        csv_attachments = {}
        csv_attachments[summary_file_name] = io.StringIO()
        df.to_csv(csv_attachments[summary_file_name] , index=False)
        csv_attachments[summary_file_name].seek(0)


        try:
            env = os.environ.get('ENV')
            outlook = Outlook()
            support_emails = [email.strip() for email in os.environ.get('REPORTS_EMAIL', '').split(',')]
            copy_emails = [email.strip() for email in os.environ.get('BCC_EMAIL', '').split(',')]
            sender = os.environ.get('REPORTER_EMAIL')
            
            subject = f"{env.upper()} - ARIA Work Item Loading for {collection_name.upper()} in Stage {stage}"
            body = outlook.generate_email_body_html()
            
            # outlook.send_email_notification(subject=subject, body=body, attachment_files=csv_attachments, emails={
            #     "sender": sender,
            #     "recipients": support_emails,
            #     "bcc": copy_emails
            # })

            return {'statusCode': 200, 'body': json.dumps({'message': f"Report for {collection_name.upper()} processed correctly."})}

        except Exception as e:
            print(f"Error sending email: {e}")
            return {'statusCode': 500, 'body': json.dumps({'message': f"Error sending email: {e}"})}

    except Exception as e:
        print("Error when procesing report: ", traceback.format_exc())
        return {'statusCode': 500, 'body': json.dumps({'message': f"Error when procesing report: {e}"})}
