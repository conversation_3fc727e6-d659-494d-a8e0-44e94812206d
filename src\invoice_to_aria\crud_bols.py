# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os

from mongo_utils import Mongo
from boto3_utils import get_secret



class CrudBols:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='bol'
        )

    def insert_bol(self, email_id, attachment_id, attachment_name, vins, path, file_ocr, wi_id):
        """
        This function inserts a folio into the database.
        """

        bol_document = {
            "email_id": email_id,
            "attachment_name": attachment_name,
            "attachment_id": attachment_id,
            "path": path,
            "vins": vins,
            "raw_ocr": file_ocr,
            "aria_wi_id": wi_id
        }

        self.mongo.insert_one(bol_document)

    def update_bol_by_wi_id(self, wi_id, data):
        query = {"aria_wi_id": wi_id}
        data = {"$set": data} 
        return self.mongo.update_one(query, data)

    def find_bol_by_id(self, attachment_id):
        """
        This function finds a bol by its attachment ID.
        """
        query = {"attachment_id": attachment_id}
        return self.mongo.find_one(query)
    
    def find_bol_by_email_id(self, bol_id):
        """
        This function finds a bol by its ID.
        """
        query = {"aria_wi_id": bol_id}
        return self.mongo.find_one(query)
    
    def find_bol_by_vin(self, vin):
        """
        This function finds a bol by its ID.
        """
        query = {"extracted_vins": vin}
        return self.mongo.find_one(query)

    def find_bols_by_filter(self, filter):
        """
        This function finds bols by a filter.
        """
        return list(self.mongo.find(filter))
    
    def __del__(self):
        self.mongo.close_connection()